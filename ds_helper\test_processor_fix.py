#!/usr/bin/env python3
"""
Test the processor fix.
"""

import sys
import shutil
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ds_helper.processor.processor import MultiSourceRAGProcessor
from ds_helper.utils.logging import setup_logging

def test_processor_fix():
    """Test the processor fix."""
    import logging
    setup_logging(logging.INFO)
    
    print("=== Testing Processor Fix ===")
    
    # Check if we have scraped files
    input_dir = Path("data/raw")
    if not input_dir.exists():
        print("❌ No data/raw directory found")
        return False
    
    # Find all text files recursively
    all_files = list(input_dir.rglob("*.txt"))
    print(f"Found {len(all_files)} text files:")
    for f in all_files:
        print(f"  - {f}")
    
    if not all_files:
        print("❌ No text files found")
        return False
    
    # Clean up any existing processed output
    if Path("test_processor_fix_output").exists():
        shutil.rmtree("test_processor_fix_output")
    
    print(f"\nTesting Processor with {len(all_files)} files...")
    
    # Test processor
    processor = MultiSourceRAGProcessor(
        input_dir="data/raw",
        output_dir="test_processor_fix_output",
        min_chunk_size=50,  # Lower minimum for testing
        max_chunk_size=1000
    )
    
    # Process files
    processor.process()
    
    # Check results
    output_dir = Path("test_processor_fix_output")
    chunks_file = output_dir / "chunks.json"
    
    if chunks_file.exists():
        import json
        with open(chunks_file, 'r', encoding='utf-8') as f:
            chunks = json.load(f)
        
        print(f"✅ Created {len(chunks)} chunks")
        
        if chunks:
            print("✅ Sample chunk:")
            sample = chunks[0]
            for key, value in sample.items():
                if key == 'embedding':
                    print(f"  {key}: [vector of length {len(value)}]")
                elif key == 'content':
                    print(f"  {key}: {value[:200]}...")
                else:
                    print(f"  {key}: {value}")
            
            # Check source distribution
            sources = {}
            for chunk in chunks:
                source = chunk.get('source', 'unknown')
                sources[source] = sources.get(source, 0) + 1
            
            print(f"✅ Source distribution: {sources}")
            
            return len(chunks) > 0
        else:
            print("❌ No chunks in file")
            return False
    else:
        print("❌ No chunks.json created")
        return False

if __name__ == "__main__":
    success = test_processor_fix()
    if success:
        print("\n🎉 Processor fix is working!")
    else:
        print("\n❌ Processor fix needs more work.")
    
    sys.exit(0 if success else 1)
