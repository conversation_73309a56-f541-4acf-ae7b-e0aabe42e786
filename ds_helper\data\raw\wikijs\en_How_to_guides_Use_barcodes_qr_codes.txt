Title: Use Barcodes and QR Codes
URL: https://datastore.bam.de/en/How_to_guides/Use_barcodes_qr_codes
Source: datastore
---

/
How_to_guides
/
Use_barcodes_qr_codes
Use Barcodes and QR Codes
Page Contents
🔧 Use Barcodes and QR Codes
✅ Prerequisites
🪪 Step 1: Choose the Code Content
🖨️ Step 2: Generate the Codes
🧾 Step 3: Print Code
📥 Step 4: Print and Apply Stickers
📡 Step 5: Scan and Use in openBIS
Last edited by
<PERSON><PERSON>, Angela
06/18/2025
¶
🔧 Use Barcodes and QR Codes
To enable fast and secure referencing of physical objects (e.g., samples, instruments) in openBIS using barcodes or QR codes.
¶
✅ Prerequisites
Access to an openBIS instance
Physical objects to label (e.g., samples, devices)
Barcode/QR code reader (USB or Bluetooth)
Sticker printer or external code generator (optional)
¶
🪪 Step 1: Choose the Code Content
When an Object is registered, a Default Barcode is automatically generated by openBIS. This is found in Identification Info.
It is also possible to use the PermId to generate a Barcode/QR code.
¶
Option A:
PermId
Pros
:
Always available and unique
Compact (suitable for Micro-QR)
Cons
:
Tied to one openBIS instance
Changes on export/import
Only available after object registration
¶
Option B:
$BARCODE Property
Pros
:
Can be pre-assigned and batch imported
Compatible across systems
Cons
:
Uniqueness not enforced
Requires additional data management
¶
🖨️ Step 2: Generate the Codes
¶
Option A:
Within openBIS ELN
Direct integration
Limited formatting
¶
Option B:
External Tools
Linux
:
qrencode
(CLI, scriptable)
Windows
:
Zint
(GUI, flexible)
Label Printer Software
:
Often supports Excel import
Good formatting and printer integration
¶
🧾 Step 3: Print Code
¶
Select the Code Format
Choose based on your hardware and space constraints:
Code Type
Format
Pros
Notes
Code 128
1D
Widely supported
Minimum standard
QR Code
2D
Compact, robust
Recommended
Micro-QR Code
2D
Very small (5x5mm)
Ideal for PermIds
💡
Tip
: Always prefer 2D codes unless you have a specific reason to use 1D.
¶
📥 Step 4: Print and Apply Stickers
Use durable stickers compatible with your printer
Include optional metadata (e.g., contact, organizational unit)
Apply to physical objects clearly and accessibly
¶
How to print Code from openBIS(tbL)
Print Barcode/QR code generated in openBIS
¶
📡 Step 5: Scan and Use in openBIS
Use barcode/QR readers (USB-HID or Bluetooth)
Scanned codes will:
Display object info
Link samples/devices in experiments
🧪 Example Use Case: Scan a sample’s QR code during an experiment to auto-link it to the experiment record in openBIS.