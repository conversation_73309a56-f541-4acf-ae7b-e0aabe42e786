#!/usr/bin/env python3
"""
Complete Processing Script for DS Helper

This script processes all scraped data from both openBIS and Wiki.js sources,
applies intelligent chunking, and prepares the data for embedding and vector storage.
It can also optionally generate embeddings and store them in ChromaDB.
"""

import sys
import logging
import argparse
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ds_helper.processor.scraped_text_processor import ScrapedTextProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def process_scraped_data(
    input_dir: str = "ds_helper/data/raw",
    output_dir: str = "ds_helper/data/processed",
    min_chunk_size: int = 100,
    max_chunk_size: int = 1000,
    chunk_overlap: int = 50
):
    """
    Process all scraped data and create chunks.
    
    Args:
        input_dir: Directory containing scraped .txt files
        output_dir: Directory to save processed chunks
        min_chunk_size: Minimum chunk size in characters
        max_chunk_size: Maximum chunk size in characters
        chunk_overlap: Overlap between chunks in characters
    
    Returns:
        Dictionary with processing results
    """
    logger.info("Starting complete data processing...")
    
    # Create processor
    processor = ScrapedTextProcessor(
        input_dir=input_dir,
        output_dir=output_dir,
        min_chunk_size=min_chunk_size,
        max_chunk_size=max_chunk_size,
        chunk_overlap=chunk_overlap
    )
    
    # Process all sources
    results = processor.process_all_sources()
    
    # Print summary
    total_chunks = sum(len(records) for records in results.values())
    logger.info(f"Processing completed successfully!")
    logger.info(f"Total chunks created: {total_chunks}")
    
    for source, records in results.items():
        logger.info(f"  {source}: {len(records)} chunks")
    
    # Calculate statistics
    if results:
        all_records = []
        for source_records in results.values():
            all_records.extend(source_records)
        
        if all_records:
            chunk_sizes = [len(record['text']) for record in all_records]
            logger.info(f"Chunk size statistics:")
            logger.info(f"  Min: {min(chunk_sizes)} characters")
            logger.info(f"  Max: {max(chunk_sizes)} characters")
            logger.info(f"  Average: {sum(chunk_sizes) // len(chunk_sizes)} characters")
            
            # Count by heading levels
            levels = {}
            for record in all_records:
                level = record['level']
                levels[level] = levels.get(level, 0) + 1
            
            logger.info(f"Chunks by heading level:")
            for level in sorted(levels.keys()):
                logger.info(f"  Level {level}: {levels[level]} chunks")
    
    return results


def generate_embeddings_and_store(
    processed_data_file: str,
    db_path: str = "ds_helper_vectordb",
    embedding_model: str = "nomic-embed-text"
):
    """
    Generate embeddings and store in ChromaDB (optional step).
    
    Args:
        processed_data_file: Path to the processed JSON file
        db_path: Path to ChromaDB database
        embedding_model: Name of the embedding model to use
    """
    try:
        import json
        import chromadb
        from chromadb.config import Settings
        
        logger.info("Loading processed data...")
        with open(processed_data_file, 'r', encoding='utf-8') as f:
            records = json.load(f)
        
        logger.info(f"Loaded {len(records)} chunks")
        
        # Initialize ChromaDB
        logger.info(f"Initializing ChromaDB at {db_path}...")
        client = chromadb.PersistentClient(
            path=db_path,
            settings=Settings(anonymized_telemetry=False)
        )
        
        # Create or get collection
        collection_name = "ds_helper_docs"
        try:
            collection = client.get_collection(collection_name)
            logger.info(f"Using existing collection: {collection_name}")
        except:
            collection = client.create_collection(
                name=collection_name,
                metadata={"description": "DS Helper documentation chunks"}
            )
            logger.info(f"Created new collection: {collection_name}")
        
        # Prepare data for ChromaDB
        ids = []
        documents = []
        metadatas = []
        
        for record in records:
            ids.append(record['id'])
            documents.append(record['text'])
            
            # Create metadata (ChromaDB doesn't support nested objects)
            metadata = {
                'title': record['title'],
                'url': record['url'],
                'source': record['source'],
                'heading': record['heading'],
                'level': record['level'],
                'chunk_index': record['chunk_index'],
                'file_path': record['file_path'],
                'timestamp': record['timestamp']
            }
            metadatas.append(metadata)
        
        # Add to collection (ChromaDB will generate embeddings automatically)
        logger.info("Adding documents to ChromaDB (this may take a while)...")
        batch_size = 100
        for i in range(0, len(ids), batch_size):
            batch_ids = ids[i:i+batch_size]
            batch_docs = documents[i:i+batch_size]
            batch_metas = metadatas[i:i+batch_size]
            
            collection.add(
                ids=batch_ids,
                documents=batch_docs,
                metadatas=batch_metas
            )
            logger.info(f"Added batch {i//batch_size + 1}/{(len(ids) + batch_size - 1)//batch_size}")
        
        logger.info(f"Successfully stored {len(records)} chunks in ChromaDB")
        
        # Test the database
        logger.info("Testing database with a sample query...")
        results = collection.query(
            query_texts=["What is openBIS?"],
            n_results=3
        )
        
        logger.info(f"Sample query returned {len(results['documents'][0])} results")
        for i, doc in enumerate(results['documents'][0]):
            logger.info(f"  Result {i+1}: {doc[:100]}...")
        
    except ImportError:
        logger.warning("ChromaDB not available. Skipping embedding generation.")
        logger.info("To enable embedding generation, install ChromaDB:")
        logger.info("  pip install chromadb")
    except Exception as e:
        logger.error(f"Error generating embeddings: {e}")
        raise


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Process all scraped data for DS Helper RAG system"
    )
    parser.add_argument(
        "--input-dir",
        default="ds_helper/data/raw",
        help="Input directory containing scraped files"
    )
    parser.add_argument(
        "--output-dir",
        default="ds_helper/data/processed",
        help="Output directory for processed chunks"
    )
    parser.add_argument(
        "--min-chunk-size",
        type=int,
        default=100,
        help="Minimum chunk size in characters"
    )
    parser.add_argument(
        "--max-chunk-size",
        type=int,
        default=1000,
        help="Maximum chunk size in characters"
    )
    parser.add_argument(
        "--chunk-overlap",
        type=int,
        default=50,
        help="Overlap between chunks in characters"
    )
    parser.add_argument(
        "--generate-embeddings",
        action="store_true",
        help="Generate embeddings and store in ChromaDB"
    )
    parser.add_argument(
        "--db-path",
        default="ds_helper_vectordb",
        help="Path to ChromaDB database"
    )
    parser.add_argument(
        "--embedding-model",
        default="nomic-embed-text",
        help="Embedding model to use"
    )
    
    args = parser.parse_args()
    
    try:
        # Process the scraped data
        results = process_scraped_data(
            input_dir=args.input_dir,
            output_dir=args.output_dir,
            min_chunk_size=args.min_chunk_size,
            max_chunk_size=args.max_chunk_size,
            chunk_overlap=args.chunk_overlap
        )
        
        if not results:
            logger.error("No data was processed. Check input directory and files.")
            return 1
        
        # Generate embeddings if requested
        if args.generate_embeddings:
            processed_file = Path(args.output_dir) / "all_chunks.json"
            if processed_file.exists():
                generate_embeddings_and_store(
                    processed_data_file=str(processed_file),
                    db_path=args.db_path,
                    embedding_model=args.embedding_model
                )
            else:
                logger.error(f"Processed data file not found: {processed_file}")
                return 1
        
        logger.info("All processing completed successfully!")
        return 0
        
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
