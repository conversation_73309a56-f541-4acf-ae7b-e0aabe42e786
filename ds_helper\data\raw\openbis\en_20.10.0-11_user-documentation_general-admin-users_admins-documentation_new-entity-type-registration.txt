Title: New Entity Type Registration
URL: https://openbis.readthedocs.io/en/20.10.0-11/user-documentation/general-admin-users/admins-documentation/new-entity-type-registration.html
Source: openbis
---

New Entity Type Registration

Entity types, i.e.
Experiment/Collection
,
Object
and
Dataset
types, as well as
Property types
and
Controlled Vocabularies
constitute the openBIS
masterdata
. They can be created by someone
with
Instance admin
role in the
new Admin openBIS UI
.
Note:
Material
types are also part of the openBIS masterdata. However,
they are no longer supported and will be decommissioned soon. They
should NOT be used.
The new Admin openBIS UI can be accessed via a URL of this type:
https://openbis-xxx/openbis/webapp/openbis-ng-ui/
where openbis-xxx is the name of the server specified in the openBIS
configuration file, during the installation by a system admin.
Register a new Object Type

Select
Types -> Object Types
from the menu.
Click
Add
at the bottom of the page.
Enter a
Code
. E.g.
INSTRUMENT
. This is the name of the
Object to create and is unique. Please note that Codes should be in
capital letters, and they can only contain A-Z, a-z, 0-9 and _, -, .
Provide a description (not mandatory).
Entity validation plugin is used when we want to have validation on
some data entries. This is done via a custom script (see
Entity Validation Scripts
)
Enter the
Generated Code Prefix
. As a convention, we recommended
to use the first 3 letters of the
Object
type
code (e.g.
INS
, in
this case). This field is used by openBIS to automatically generate
Object codes: the codes will be for example INS1, INS2, INS3, etc.
Leave
Generate Codes
selected if you want to have codes
automatically generated by openBIS.
Unique Subcodes
is used for contained objects, which are not
used in the ELN. Ignore this option.
Click
Add Section
at the bottom of the page. Sections are ways
of grouping together similar properties. Examples of sections used in
the ELN are
General info
,
Storage info
,
Experimental Details
, etc.
Add properties inside the Section, by clicking the
Add Property
button at the bottom of the page. To remove a property, use the
Remove
button at the bottom of the page.
Click
Save
at the bottom of the page.
Please note that new
Object types
created in the admin UI, do not
automatically appear in ELN drop downs, but they have to be manually
enabled, as described here:
Enable Objects in dropdowns
Registration of Properties

When registering new properties, the fields below need to be filled in.
Code.
Unique identifier of the property. Codes can only contain
A-Z, a-z, 0-9 and _, -, .
Data Type.
See below for data types definitions.
Label.
This is the property/column header that the user can see
in the ELN.
Description
: The description is shown inside a field, to give
hints about the field itself. In most cases, label and description
can be the same.
Dynamic Property Plugin
: Script for calculated properties.
See
Dynamic properties
Editable
: Editable in the ELN interface. In some cases, metadata
is automatically imported by scripts and this should not be changed
by users in the interface.
Mandatory
: Field can be set as mandatory.
Property Data Types

BOOLEAN
: yes or no
CONTROLLEDVOCABULARY
: list of predefined values
DATE
. Date field
HYPERLINK
: URL
INTEGER
: integer number
MATERIAL
. Not used in ELN. It will be dismissed.
MULTILINE_VARCHAR
: long text. It is possible to enable a Rich
Text Editor for this type of property. This is described
here:
Enable Rich Text Editor or Spreadsheet Widgets
REAL
: decimal number
OBJECT
. 1-1 connection to a specific object type.
TIMESTAMP
: date with timestamp
VARCHAR
: one-line text
XML
: to be used by
Managed Properties
(see
openBIS Managed Properties
and for
Spreadsheet component
s, as described here:
Enable Rich Text Editor or Spreadsheet Widgets
Considerations on properties registration

If you create a property with code “PROJECT”, you should not use the
label “Project”. This will give an error if you use XLS Batch
registration/update, because openBIS considers this to be an openBIS
Project
.
You should not assign more than 1 property with same label to the
same
Object
type. If two or more properties with the same label
are present in the same
Object
type, this will result in an error
in the XLS Batch registration/update.
Controlled Vocabularies

Controlled vocabularies are pre-defined lists of values for a given
field.
Existing Vocabularies can be visualised from the Types ->
Vocabularies Tab. Vocabularies staring with the “
$
” symbol are
internal: they cannot be deleted and their terms cannot be deleted.
However, it is possible to add new terms to these vocabularies and these
can also be deleted.
New Vocabularies can be added, by clicking the
Add
button at the
bottom of the page.
When registering a new vocabulary, a Code for the vocabulary needs to be
entered. This corresponds to the name of the vocabulary, and it is a
unique identifier. Codes can only contain A-Z, a-z, 0-9 and _, -, .
To add terms to the list click
Add Term
at the bottom of the page.
Vocabulary terms always have a code and a label: the code is unique and
contain only alpha-numeric characters; labels are not necessarily unique
and allow also special characters. If the label is not defined, codes
are shown.
After creating the vocabulary and registering the terms, remember to
Save
.
Register a new Experiment/Collection type

The registration of a new
Collection
type is very similar to the
registration of
Object
types. For Collection Types, you only need to
provide a Code (which is a unique identifier), Description and add a
validation plugin if you want to have metadata validation (see
Entity
Validation Scripts
).
Register a new Dataset type

The registration of a new Dataset types is similar to the registration
of object types.
It is possible to disallow deletion for a given dataset type.
Enable Rich Text Editor or Spreadsheet Widgets

For certain fields, it is possible to enable the use of a Rich Text
Editor (RTE) or a spreadsheet component.
Instance admin
rights are
necessary for this.
The
RTE
can be enabled for properties of type
MULTILINE_VARCHAR
. The
spreadsheet component
can be enabled for
properties of type
XML
.
Procedure:
Properties are defined when creating new entity types (
Datasets
,
Objects
,
Experiments/Collections
)
To set a property as RTE or spreadsheet go to the
Settings
,
under
Utilities
Select /ELN_SETTINGS/GENERAL_ELN_SETTINGS
Enable editing and scroll down to the
Custom Widgets
section
Click the + button on the same line as
Property Type
and
Widget
, as shown below
A new field will appear where you can select the property type and
the widget. Choices are:
Word Processor
(=RTE) or
Spreadsheet.
Updated on October 19, 2022
Enable Objects in dropdowns

By default, no Object shows in dropdown menus. Which object types should
show in dropdown menus can be customised from the Settings.
Navigate to the Object Type definitions Extension
Open one Object Type (e.g. Antibody)
Select show in drop downs
Save the Settings
Updated on October 19, 2022
Register masterdata via Excel

It is possible to register openBIS masterdata using an Excel template
from the admin UI.
This can be done from the Import menu under the Tools sections, as shown
below. Three options can be chosen for the import:
fail if exists
: if a type or a property already exists in the
database, the upload will fail.
ignore if exists
: if a type or a property already exists in the
database, the upload will ignore this.
update is exists
: if a type or a property already exists in the
database, the upload will update existing values.
An example template of an Excel masterdata file can be found here:
masterdata-template
Please note that in the template we used separate spreadsheets for each
type (Sample, Experiment, Dataset), but it is also possible to have
everything in one single spreadsheet.
Modifying existing types

If you wish to add a new property to an existing
Collection/Object/Dataset
type, you need to:
1. add the property in the file
2. increase the version number of the
Collection/Object/Dataset
type
3. use
Ignore if exists
as upload method. In this case, only the
new property is added to the type.
The import on the admin UI allows to register entities in addition to
masterdata. An example template file for this can be found here:
masterdata-metadata
More extensive documentation on the XLS format for masterdata and
metadata registration can be found
here
.
Updated on January 13, 2023
Properties overview

The overview of all properties registered in openBIS and their
assignments to types is available under the
Types
section in the
admin UI, as shown below.
Updated on March 1, 2022
Internal properties and vocabularies

Some properties and vocabularies are internally defined in openBIS.
These can be identified by the
$
sign.
Internal properties (e.g. $NAME, $BARCODE, etc) cannot be deleted nor
modified, not even by an instance admin.
Internal vocabularies (e.g. $DEFAULT_COLLECTION_VIEWS, etc), cannot be
deleted and their existing terms cannot be deleted nor modified, however
an instance admin can add new terms to an internal vocabulary.
Updated on January 5, 2023