Title: Batch registration/update of storage position(s)
URL: https://datastore.bam.de/en/How_to_guides/Batch_registration_storage_position
Source: datastore
---

/
How_to_guides
/
Batch_registration_storage_position
Batch registration/update of storage position(s)
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
08/19/2025
To allocate storage position to multiple Objects, the Data Store Steward(s) for your group must customize the
storage for Objects
and
Enable object types in drop-downs
so that
Allowed object types
are displayed in Excel spreadsheet for batch registration.
To assign a storage location to multiple objects, navigate to the relevant
Collection
and select
XLS Batch Register Objects
from the
More
drop-down menu. Ensure that the required Object Types are displayed in the
Register Objects
window and click on
Download
. The Excel Template should contain at least two sheets (the SAMPLE and STORAGE_POSITION metadata). To link these sheets, the information in the $ column of the SAMPLE spreadsheet must match the
Parents
column in STORAGE_POSITION spreadsheet. Enter the numbers or letters proceeded by the $ symbol (i.e., $1, $2) in the $ column. Upload the updated file and click
Accept
.
To find the
storage Code
required in the STORAGE_POSITION spreadsheet, navigate to
Utilities
,
Settings
(choose your division’s number), open the
Storages
section and select relevant storage to view its metadata. Enter the required information and save the file locally on your device.
Select Collection
Open More drop-down menu
Select XLS Batch Register Objects
Click on Template Download
(The Excel Template should contain at least two sheets,
the SAMPLE and STORAGE_POSITION metadata.)
Upload the file
Review the entries and click Accept.