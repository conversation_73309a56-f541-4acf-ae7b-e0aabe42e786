#!/usr/bin/env python3
"""
Debug the scraper to see what content is being extracted.
"""

import sys
from pathlib import Path
import requests
from bs4 import BeautifulSoup

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ds_helper.scraper.readthedocs_scraper import ReadTheDocsParser

def debug_scraper():
    """Debug what the scraper is extracting."""
    print("=== Debug Scraper ===")
    
    url = "https://openbis.readthedocs.io/en/20.10.0-11/"
    
    # Fetch the page
    response = requests.get(url)
    html_content = response.text
    
    print(f"Fetched page, size: {len(html_content)} characters")
    
    # Parse with BeautifulSoup to see structure
    soup = BeautifulSoup(html_content, "html.parser")
    
    print(f"Title: {soup.title.string if soup.title else 'No title'}")
    
    # Check what content selectors find
    parser = ReadTheDocsParser()
    
    print("\nTesting content selectors:")
    for selector in parser.content_selectors:
        elements = soup.select(selector)
        print(f"  {selector}: {len(elements)} elements found")
        if elements:
            element = elements[0]
            text = element.get_text()[:200]
            print(f"    First 200 chars: {text}")
    
    # Use the parser to extract content
    print("\n=== Using Parser ===")
    content_dict = parser.extract_content(html_content, url)
    
    print(f"Extracted title: {content_dict['title']}")
    print(f"Extracted content length: {len(content_dict['content'])}")
    print(f"First 500 chars of content: {content_dict['content'][:500]}")
    
    # Show the full content structure
    if content_dict['content']:
        lines = content_dict['content'].split('\n')
        print(f"\nContent has {len(lines)} lines")
        print("First 10 lines:")
        for i, line in enumerate(lines[:10]):
            print(f"  {i+1}: {repr(line)}")

if __name__ == "__main__":
    debug_scraper()
