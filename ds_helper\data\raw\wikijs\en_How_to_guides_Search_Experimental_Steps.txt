Title: Search for: Objects - Experimental Steps in the ELN & save search queries
URL: https://datastore.bam.de/en/How_to_guides/Search_Experimental_Steps
Source: datastore
---

/
How_to_guides
/
Search_Experimental_Steps
Search for: Objects - Experimental Steps in the ELN & save search queries
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
06/10/2025
Open the
Utilities
drop-down menu and select
Advanced Search
. Click on
Search For
drop-down menu and select Experiment/Collection, select an operator from the
Using
drop-down menu (e.g., AND), select
Field Type
option (e.g., All), enter
Field Value
(e.g., your BAM username to search for all Collections you have registered). Click the
+
icon to narrow the search further, select
Field Type
option (e.g., Property) and
Field Name
option (e.g., Modification Date. A list of all available properties becomes available), enter option for a
Comparator Operator
(e.g., thatISLaterThan (Date)) and select the
date
in
calendar
icon. Click on the
Search
icon to activate the search. Search values can also be excluded from the search by selection the NOT checkbox.
To save a search query in your own
Lab Notebook's
Space. Click on the
Save
icon displayed in the upper part of the
Advance Search
form, the
Save Search query
window will open, fill out search
Name
and start typing in the
search entity to store query
to find the name of a
Collection
. To save the search, click Save. Saved searchers are available in the drop-down menu displayed at the top of the Advanced Search page.
Open Utilities
Select Advanced Search
Click on Search For and select Experiment/Collection
Select operator from Using drop-down menu (e.g., AND)
Select Field Type option (e.g., All)
Enter Field Value (e.g., your BAM username)
Click on the + icon; select Field Type option (e.g., Property; Field Name option (i.e., Modification Date);
enter option for a Comparator Operator (e.g., thatISLaterThan (Date)) and select the date in calendar icon
Click on search icon
Click on the Save icon
Enter search Name and entity to store query (Collection Name)