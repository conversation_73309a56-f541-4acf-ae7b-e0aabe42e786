#!/usr/bin/env python3
"""
Debug Wiki.js HTML structure to understand content extraction.
"""

import requests
from bs4 import BeautifulSoup

def debug_wikijs_structure():
    """Debug the Wiki.js HTML structure."""
    
    url = "https://datastore.bam.de/en/home"
    
    print(f"Fetching: {url}")
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, "html.parser")
        
        print(f"Page title: {soup.title.string if soup.title else 'No title'}")
        
        # Look for different content containers
        potential_selectors = [
            "div.v-content__wrap",
            "div.page-content", 
            "main.v-content",
            "article",
            "div.content",
            "div.wiki-content",
            "main",
            "div.container",
            "div.v-container",
            "div.page",
            "div.wiki-page",
            "body",
        ]
        
        print(f"\nTesting content selectors:")
        for selector in potential_selectors:
            elements = soup.select(selector)
            if elements:
                element = elements[0]
                text_content = element.get_text(strip=True)
                print(f"  ✓ {selector}: Found {len(elements)} elements, text length: {len(text_content)}")
                if len(text_content) > 100:
                    print(f"    Preview: {text_content[:200]}...")
            else:
                print(f"  ✗ {selector}: Not found")
        
        # Look for specific Wiki.js patterns
        print(f"\nLooking for Wiki.js specific elements:")
        wikijs_patterns = [
            "script[type='application/ld+json']",
            "div[id*='app']",
            "div[class*='wiki']",
            "div[class*='page']",
            "div[class*='content']",
            "main",
        ]
        
        for pattern in wikijs_patterns:
            elements = soup.select(pattern)
            if elements:
                print(f"  ✓ {pattern}: Found {len(elements)} elements")
                for i, elem in enumerate(elements[:2]):  # Show first 2
                    if elem.name == 'script':
                        print(f"    Script {i}: {elem.string[:100] if elem.string else 'No content'}...")
                    else:
                        text = elem.get_text(strip=True)
                        print(f"    Element {i}: {len(text)} chars - {text[:100]}...")
            else:
                print(f"  ✗ {pattern}: Not found")
        
        # Check if it's a single-page application
        print(f"\nChecking for SPA indicators:")
        spa_indicators = soup.select("script[src*='app'], script[src*='chunk'], div[id='app'], div[id='__nuxt']")
        if spa_indicators:
            print(f"  This appears to be a Single Page Application (SPA)")
            print(f"  Found {len(spa_indicators)} SPA indicators")
        else:
            print(f"  This appears to be a traditional server-rendered page")
        
        # Try to find the actual content in the raw HTML
        print(f"\nSearching for content in raw HTML:")
        content_keywords = ["Welcome to the Data Store Wiki", "What is the Data Store", "Concepts", "How-to guides"]
        for keyword in content_keywords:
            if keyword in response.text:
                print(f"  ✓ Found '{keyword}' in raw HTML")
                # Find the context around this keyword
                start = response.text.find(keyword)
                context = response.text[max(0, start-100):start+200]
                print(f"    Context: ...{context}...")
            else:
                print(f"  ✗ '{keyword}' not found in raw HTML")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    debug_wikijs_structure()
