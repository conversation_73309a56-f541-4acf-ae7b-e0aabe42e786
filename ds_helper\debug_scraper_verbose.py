#!/usr/bin/env python3
"""
Debug the scraper with verbose output.
"""

import sys
import shutil
from pathlib import Path
import logging

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ds_helper.scraper.readthedocs_scraper import ReadTheDocsScraper
from ds_helper.utils.logging import setup_logging

def debug_scraper():
    """Debug the scraper with verbose logging."""
    
    # Set up verbose logging
    setup_logging(logging.DEBUG)
    
    print("=== Debug Scraper with Verbose Logging ===")
    
    # Clean up any existing test output
    if Path("debug_scraper_output").exists():
        shutil.rmtree("debug_scraper_output")
    
    print("\nTesting Scraper with verbose logging...")
    
    # Test scraper with the new URL and limited pages
    scraper = ReadTheDocsScraper(
        base_url="https://openbis.readthedocs.io/en/20.10.0-11/index.html",
        output_dir="debug_scraper_output",
        max_pages=3  # Test with just 3 pages
    )
    
    print(f"Base URL: {scraper.base_url}")
    print(f"Domain: {scraper.domain}")
    print(f"URLs to visit initially: {scraper.urls_to_visit}")
    
    # Run the scraper
    scraper.scrape()
    
    # Check results
    output_dir = Path("debug_scraper_output")
    if output_dir.exists():
        scraped_files = list(output_dir.glob("*.txt"))
        print(f"\n✅ Successfully scraped {len(scraped_files)} files:")
        for f in scraped_files:
            print(f"  - {f.name}")
            
        print(f"\nFinal state:")
        print(f"  Visited URLs: {len(scraper.visited_urls)}")
        print(f"  Remaining URLs to visit: {len(scraper.urls_to_visit)}")
        
        if scraper.visited_urls:
            print(f"  Visited URLs:")
            for url in list(scraper.visited_urls)[:5]:
                print(f"    - {url}")
        
        if scraper.urls_to_visit:
            print(f"  Remaining URLs:")
            for url in scraper.urls_to_visit[:5]:
                print(f"    - {url}")
        
        return len(scraped_files) > 1
    else:
        print("❌ No files scraped")
        return False

if __name__ == "__main__":
    success = debug_scraper()
    if success:
        print("\n🎉 Scraper is working!")
    else:
        print("\n❌ Scraper needs more work.")
    
    sys.exit(0 if success else 1)
