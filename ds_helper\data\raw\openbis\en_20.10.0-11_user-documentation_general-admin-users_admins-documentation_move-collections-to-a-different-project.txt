Title: Move Collections to a different Project
URL: https://openbis.readthedocs.io/en/20.10.0-11/user-documentation/general-admin-users/admins-documentation/move-collections-to-a-different-project.html
Source: openbis
---

Move Collections to a different Project

It is possible to move one
Collection
with it entire content
(
Objects
+
Datasets
) from one
Project
to another.
If
Objects
contain parent/child relationships these are preserved.
This operation requires
Space Power User
or
Admin
rights.
To move
Collections
in the Inventory:
Go to the
Collection
page you want to move
Click on
Edit Collection
3. Select
Move
from the
More..
dropdown
4. Enter the code of the
Project
where you want to move your
Collection
. If you start typing the code, openBIS will prompt you with
a list of available options and you can select the appropriate one from
there.
5. Click
Accept
Updated on April 26, 2023