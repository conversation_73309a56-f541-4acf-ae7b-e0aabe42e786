#!/usr/bin/env python3
"""
Quick test of the scraper with improved selectors.
"""

import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ds_helper.scraper.readthedocs_scraper import ReadTheDocsScraper
from ds_helper.processor.processor import MultiSourceRAGProcessor

def quick_test():
    """Quick test of scraper and processor."""
    print("=== Quick Test ===")
    
    # Clean up any existing test output
    import shutil
    for path in ["quick_test_output", "quick_test_processed"]:
        if Path(path).exists():
            shutil.rmtree(path)
    
    # Test scraper
    scraper = ReadTheDocsScraper(
        base_url="https://openbis.readthedocs.io/en/20.10.0-11/",
        output_dir="quick_test_output",
        max_pages=2
    )
    
    scraper.scrape()
    
    # Check scraped files
    output_dir = Path("quick_test_output")
    if output_dir.exists():
        scraped_files = list(output_dir.glob("*.txt"))
        print(f"Scraped {len(scraped_files)} files:")
        
        for f in scraped_files:
            print(f"  - {f.name}")
            with open(f, 'r', encoding='utf-8') as file:
                content = file.read()
                print(f"    Size: {len(content)} characters")
                
                # Show content structure
                lines = content.split('\n')
                metadata_end = 0
                for i, line in enumerate(lines):
                    if line.startswith('---'):
                        metadata_end = i + 1
                        break
                
                if metadata_end > 0:
                    actual_content = '\n'.join(lines[metadata_end:]).strip()
                    print(f"    Content after metadata: {len(actual_content)} characters")
                    if actual_content:
                        print(f"    First 300 chars of content: {actual_content[:300]}")
                    else:
                        print("    No content after metadata!")
                print("    ---")
        
        # Test processor if we have content
        if scraped_files:
            print("\n=== Testing Processor ===")
            
            processor = MultiSourceRAGProcessor(
                input_dir="quick_test_output",
                output_dir="quick_test_processed",
                min_chunk_size=30,  # Very small for testing
                max_chunk_size=300
            )
            
            processor.process()
            
            # Check processed files
            processed_dir = Path("quick_test_processed")
            chunks_file = processed_dir / "chunks.json"
            if chunks_file.exists():
                import json
                with open(chunks_file, 'r', encoding='utf-8') as f:
                    chunks = json.load(f)
                print(f"Created {len(chunks)} chunks")
                
                if chunks:
                    print("Sample chunk:")
                    sample = chunks[0]
                    for key, value in sample.items():
                        if key == 'embedding':
                            print(f"  {key}: [vector of length {len(value)}]")
                        elif key == 'content':
                            print(f"  {key}: {value}")
                        else:
                            print(f"  {key}: {value}")
            else:
                print("No chunks.json created")
    else:
        print("No output directory created")

if __name__ == "__main__":
    quick_test()
