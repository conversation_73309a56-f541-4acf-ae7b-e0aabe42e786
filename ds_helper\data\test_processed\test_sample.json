[{"id": "en_20.10.0-11_software-developer-documentation_apis_index_0", "title": "APIS", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/index.html", "source": "openbis", "text": "Session Information\n\nExample\nCreating entities\nExample\nProperties example\nDifferent ids example\nParent child example\nUpdating entities\nExample\nProperties example\nParents example\nGetting authorization rights for entities\nFreezing entities\nSpace\nProject\nExperiment\nSample", "heading": "Session Information", "level": 3, "chunk_index": 0, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_index.txt", "timestamp": "2025-09-16T17:51:18.963312"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_index_1", "title": "APIS", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/index.html", "source": "openbis", "text": "Example with OR operator\n\nExample with nested logical operators\nExample with recursive fetch options\nGlobal search\nGetting entities\nExample\nDeleting entities\nExample\nSearching entity types\nModifications", "heading": "Example with OR operator", "level": 3, "chunk_index": 1, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_index.txt", "timestamp": "2025-09-16T17:51:18.963312"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_index_2", "title": "APIS", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/index.html", "source": "openbis", "text": "Custom AS Services\n\nSearch for custom services\nExecute a custom service\nArchiving / unarchiving data sets\nArchiving data sets\nUnarchiving data sets", "heading": "Custom AS Services", "level": 3, "chunk_index": 2, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_index.txt", "timestamp": "2025-09-16T17:51:18.963312"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_index_3", "title": "APIS", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/index.html", "source": "openbis", "text": "Executing Operations\n\nMethod executeOperations\nMethod getOperationExecutions / searchOperationExecutions\nMethod updateOperationExecutions / deleteOperationExecutions\nConfiguration", "heading": "Executing Operations", "level": 3, "chunk_index": 3, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_index.txt", "timestamp": "2025-09-16T17:51:18.963312"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_index_4", "title": "APIS", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/index.html", "source": "openbis", "text": "Simple Downloading\n\nDownload a single file located inside a dataset\nDownload a folder located inside a dataset\nSearch for a dataset and download all its contents, file by file\nDownload a whole dataset recursively\nSearch and list all the files inside a data store", "heading": "Simple Downloading", "level": 3, "chunk_index": 4, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_index.txt", "timestamp": "2025-09-16T17:51:18.963312"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_index_5", "title": "APIS", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/index.html", "source": "openbis", "text": "General Usage\n\nTAB completition and other hints in Jupyter / IPython\nChecking input\nGlossary\nconnect to OpenBIS\nlogin\nVerify certificate\nCheck session token, logout()\nAuthentication without user/password\nPersonal access token (PAT)\nCaching\nMount openBIS dataStore server", "heading": "General <PERSON><PERSON>", "level": 3, "chunk_index": 5, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_index.txt", "timestamp": "2025-09-16T17:51:18.963312"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_index_6", "title": "APIS", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/index.html", "source": "openbis", "text": "Prerequisites: FUSE / SSHFS\n\nMount dataStore server with pyBIS\nMasterdata\nbrowse masterdata\ncreate property types\ncreate sample types / object types\nassign and revoke properties to sample type / object type\ncreate a dataset type\ncreate an experiment type / collection type\ncreate material types\ncreate plugins", "heading": "Prerequisites: FUSE / SSHFS", "level": 3, "chunk_index": 6, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_index.txt", "timestamp": "2025-09-16T17:51:18.963312"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_index_7", "title": "APIS", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/index.html", "source": "openbis", "text": "Experiments / Collections\n\ncreate a new experiment\nsearch for experiments\nExperiment attributes\nExperiment properties", "heading": "Experiments / Collections", "level": 3, "chunk_index": 7, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_index.txt", "timestamp": "2025-09-16T17:51:18.963312"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_index_8", "title": "APIS", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/index.html", "source": "openbis", "text": "Samples / Objects\n\ncreate/update/delete many samples in a transaction\nparents, children, components and container\nsample tags\nSample attributes and properties\nsearch for samples / objects\nfreezing samples\nDatasets\nworking with existing dataSets\ndownload dataSets\nlink dataSets\ndataSet attributes and properties\nsearch for dataSets\nfreeze dataSets\ncreate a new dataSet\ncreate dataSet with zipfile\ncreate dataSet with mixed content\ncreate dataSet container\nget, set, add and remove parent datasets\nget, set, add and remove child datasets\ndataSet containers", "heading": "Samples / Objects", "level": 3, "chunk_index": 8, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_index.txt", "timestamp": "2025-09-16T17:51:18.963312"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_index_9", "title": "APIS", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/index.html", "source": "openbis", "text": "Custom Widgets\n\nThings object\nJSON response\nDataFrame\nObjects\nBest practices\nLogout\nIteration over tree structure\nIteration over raw data\nMatlab (V3 API) - How to access openBIS from MATLAB\nPreamble\nSetup\nmacOS\nWindows 10\nUsage\nNotes", "heading": "Custom Widgets", "level": 3, "chunk_index": 9, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_index.txt", "timestamp": "2025-09-16T17:51:18.963312"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_index_10", "title": "APIS", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/index.html", "source": "openbis", "text": "Personal Access Tokens\n\nBackground\nWhat are “Personal access tokens” ?\nWho can create a “Personal access token” ?\nWhere can I use “Personal access tokens” ?\nWhere “Personal access tokens” are stored ?\nHow long should my “Personal Access Tokens” be valid ?\nConfiguration", "heading": "Personal Access Tokens", "level": 3, "chunk_index": 10, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_index.txt", "timestamp": "2025-09-16T17:51:18.963312"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_0", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "I. Architecture\n\n\nOpen BIS consists of two main components: an Application Server and one\nor more Data Store Servers. The Application Server manages the system’s\nmeta data, while the Data Store Server(s) manage the file store(s). Each\nData Store Server manages its own file store. Here we will refer to the\nApplication Server as the “AS” and the Data Store Server as the “DSS.”", "heading": "I. Architecture", "level": 3, "chunk_index": 0, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.994868"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_1", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "One AS, one or more DSS\n\n\nWhy is there only one Application Server but multiple Data Store\nServers? It is possible to have only one Data Store Server, but in a\ncomplex project there might be many labs using the same OpenBIS instance\nand therefore sharing the same meta data. Each lab might have its own\nData Store Server to make file management easier and more efficient. The\nData Store Servers are on different Java virtual machines, which enables\nthe files to be processed faster. It is also more efficient when the\nphysical location of the Data Store Server is closer to the lab that is\nusing it. Another reason is that the meta data tends to be relatively\nsmall in size, whereas the files occupy a large amount of space in the\nsystem.", "heading": "One AS, one or more DSS", "level": 3, "chunk_index": 1, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.994868"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_2", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "The Java V3 API consists of two interfaces:\n\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerAPI\nch.ethz.sis.openbis.generic.dssapi.v3.IDatastoreServerAPI\nPlease check our JavaDoc for more", "heading": "The Java V3 API consists of two interfaces:", "level": 3, "chunk_index": 2, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.994868"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_3", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "details:\n\nhttps://openbis.ch/javadoc/20.10.x/javadoc-api-v3/index.html\nAll V3 API jars are packed in openBIS-API-V3-\n.zip which\nis part of openBIS-clients-and-APIs-\n.zip (the latest version can be downloaded at\nhttps://unlimited.ethz.ch/display/openbis/Production+Releases\n)", "heading": "details:", "level": 3, "chunk_index": 3, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.994868"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_4", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "The Javascript V3 API consists of a module hosted at\n\n<OPENBIS_URL>/resources/api/v3/openbis.js, for instance\nhttp://localhost/openbis\n/ resources/api/v3/openbis.js. Please check\nthe openbis.js file itself for more details.", "heading": "The Javascript V3 API consists of a module hosted at", "level": 3, "chunk_index": 4, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.994868"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_5", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "features:\n\nCreation:  Create spaces, projects, experiments and experiment\ntypes, samples and sample types, materials and material types,\nvocabulary terms, tags\nAssociations: Associate spaces, project, experiments, samples,\ndatasets, materials to each other\nTags: Add/Remove/Set tags for experiments, samples, datasets and\nmaterials\nProperties: Set properties for experiments, samples, datasets and\nmaterials\nSearch: Search & get spaces, project, experiments, samples,\ndatasets, materials, vocabulary terms, tags\nUpdate: Update spaces, project, experiments, samples, datasets,\nmaterials, vocabulary terms, tags\nDeletion: Delete spaces, project, experiments, samples, datasets,\nmaterials, vocabulary terms, tags\nAuthentication: Login as user, login as another user, login as an\nanonymous user\nTransactional features: performing multiple operations in one\ntransaction (with executeOperations method)\nQueries: create/update/get/search/delete/execute queries\nGenerating codes/permids", "heading": "features:", "level": 3, "chunk_index": 5, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.994868"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_6", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "the following features:\n\nManagement features: Managing data stores\nSearch features: Searching experiments having samples/datasets,\nsearching datasets (oldest, deleted, for archiving etc.)\nUpdate features: Updating datasets share id, size, status, storage\nconfirmation, post registration status", "heading": "the following features:", "level": 3, "chunk_index": 6, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.994868"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_7", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "III. Accessing the API\n\n\nIn order to use V3 API you have to know the url of an openBIS instance\nyou want to connect to. Moreover, before calling any of the API methods\nyou have to login to the system to receive a sessionToken. All the login\nmethods are part of the AS API. Once you successfully authenticate in\nopenBIS you can invoke other methods of the API (at both AS and DSS). In\neach call you have to provide your sessionToken. When you have finished\nworking with the API you should call logout method to release all the\nresources related with your session.\nNote: If the openBIS instance you are connecting to uses SSL and does\nnot have a real certificate (it is using the self-signed certificate\nthat comes with openBIS), you need to tell the java client to use the\ntrust store that comes with openBIS. This can be done by setting the\nproperty\njavax.net", "heading": "III. Accessing the API", "level": 3, "chunk_index": 7, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.994868"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_8", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ".ssl.trustStore. Example:\n\nUsing openBIS trust store in Java clients\njava\n-Djavax.net.ssl.trustStore\n=\n/home/<USER>/openbis/servers/openBIS-server/jetty/etc/openBIS.keystore\n-jar\nthe-client.jar", "heading": ".ssl.trustStore. Example:", "level": 3, "chunk_index": 8, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.994868"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_9", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Connecting in Java\n\n\nIn order to connect to openBIS V3 API in Java you can:\nuse IApplicationServerApi (AS) and IDataStoreServerApi (DSS) interfaces directly\nuse OpenBIS facade (that talks to IApplicationServerApi and IDataStoreServerApi interfaces internally)\nUsing the OpenBIS facade has some advantages over using the AS and DSS interfaces directly:\nit hides the details of the protocol and the data serialization format used between the client and the server\nit does not require you to know V3 API endpoints for both AS and DSS and their URLs\nit provides additional utility methods (e.g. getManagedPersonalAccessToken)\nBecause of these reasons, OpenBIS facade is the recommended way of connecting to V3 API in Java.\nCode examples for both approaches are presented below.\nV3ConnectionExampleUsingASAndDSSInterfaces.java\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.Space\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.fetchoptions.SpaceFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.search.SpaceSearchCriteria\n;\nimport\nch.systemsx.cisd.common.spring.HttpInvokerUtils\n;\npublic\nclass\nV3ConnectionExampleUsingASAndDSSInterfaces\n{\nprivate\nstatic\nfinal\nString\nURL\n=\n\"http://localhost:8888/openbis/openbis\"\n+\nIApplicationServerApi\n.", "heading": "Connecting in Java", "level": 3, "chunk_index": 9, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.994868"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_10", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "TIMEOUT\n\n=\n10000\n;\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// get a reference to AS API\nIApplicationServerApi\nv3\n=\nHttpInvokerUtils\n.\ncreateServiceStub\n(\nIApplicationServerApi\n.\nclass\n,\nURL\n,", "heading": "TIMEOUT", "level": 2, "chunk_index": 10, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.994868"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_11", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "TIMEOUT\n\n);\n// login to obtain a session token\nString\nsessionToken\n=\nv3\n.\nlogin\n(\n\"admin\"\n,\n\"password\"\n);\n// invoke other API methods using the session token, for instance search for spaces\nSearchResult\n<\nSpace\n>\nspaces\n=\nv3\n.\nsearchSpaces\n(\nsessionToken\n,\nnew\nSpaceSearchCriteria\n(),\nnew\nSpaceFetchOptions\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Number of spaces: \"\n+\nspaces\n.\ngetObjects\n().\nsize\n());\n// logout to release the resources related with the session\nv3\n.\nlogout\n(\nsessionToken\n);\n}\n}\nV3ConnectionExampleUsingOpenBISFacade.java\nimport\nch.ethz.sis.openbis.generic.OpenBIS\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.Space\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.fetchoptions.SpaceFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.search.SpaceSearchCriteria\n;\npublic\nclass\nV3ConnectionExampleUsingOpenBISFacade\n{\nprivate\nstatic\nfinal\nString\nURL\n=\n\"http://localhost:8888\"\n;\nprivate\nstatic\nfinal\nint", "heading": "TIMEOUT", "level": 2, "chunk_index": 11, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.994868"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_12", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "TIMEOUT\n\n=\n10000\n;\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// create OpenBIS facade (it aggregates methods from both IApplicationServerApi and IDataStoreServerApi)\nOpenBIS\nv3\n=\nnew\nOpenBIS\n(\nURL\n,", "heading": "TIMEOUT", "level": 2, "chunk_index": 12, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.994868"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_13", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "TIMEOUT\n\n);\n// login to obtain a session token (the token is stored in the facade and used for subsequent calls)\nv3\n.\nlogin\n(\n\"admin\"\n,\n\"password\"\n);\n// invoke other API methods, for instance search for spaces\nSearchResult\n<\nSpace\n>\nspaces\n=\nv3\n.\nsearchSpaces\n(\nnew\nSpaceSearchCriteria\n(),\nnew\nSpaceFetchOptions\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Number of spaces: \"\n+\nspaces\n.\ngetObjects\n().\nsize\n());\n// logout to release the resources related with the session\nv3\n.\nlogout\n();\n}\n}", "heading": "TIMEOUT", "level": 2, "chunk_index": 13, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_14", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Connecting in Javascript\n\n\nWe have put a lot of effort to make the use of the API in Javascript and Java almost identical. The DTOs which are a big part of the API are exactly\nthe same in both languages. The methods you can invoke via the Javascript and Java APIs are also exactly the same. This makes the switch from\nJavascript to Java or the other way round very easy. Because of some major differences between Javascript and Java development still some things had\nto be done a bit differently. But even then we tried to be conceptually consistent.\nBefore we go into details let’s mention that there are actually 4 different ways the Javascript V3 API can be loaded and used. These are:", "heading": "Connecting in Javascript", "level": 3, "chunk_index": 14, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_15", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "AMD / RequireJS\n\nAMD / RequireJS bundle\nVAR bundle\nESM bundle\nIMPORTANT: VAR and ESM bundles are currently the recommended way of using the Javascript V3 API. AMD / RequireJS approach is still supported but no\nlonger recommended.", "heading": "AMD / RequireJS", "level": 3, "chunk_index": 15, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_16", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "AMD / RequireJS\n\n\nInitially, the only way to load and use the V3 API in Javascript was based on AMD modules and RequireJS (see code example below). In that approach,\nwhat we had to do first was to load RequireJS library itself and its config. Once that was done, we could start loading all the necessary V3 API\nclasses and the V3 API facade to make our V3 API calls.\nThis approach worked fine, but there were also some drawbacks:\neach V3 API class we wanted to use had to be explicitly “required” and its full class name had to be provided (e.g. as/dto/space/Space)\nclasses were loaded asynchronously making the code using the V3 API more complex\nevery V3 API class was loaded with a separate HTTP request to the server (loading multiple classes resulted in multiple requests to the server)\nit required a third party dependency manager (here RequireJS)\nBecause of these shortcomings this approach is no longer recommended, but still fully supported. Please use VAR or ESM bundles instead\n(depending on your use case).\nV3ConnectionExampleUsingRequireJS.html\n<!DOCTYPE html>\n<\nhtml\n>\n<\nhead\n>\n<\nmeta\ncharset\n=\n\"utf-8\"\n>\n<\ntitle\n>\nV3ConnectionExampleUsingRequireJS\n</\ntitle\n>\n<!--\nThese two js files, i.e. config.js and require.js are RequireJS configuration and RequireJS library itself.\nPlease check http://requirejs.org/ for more details on how RequireJS makes loading dependencies in Javascript easier.\n-->\n<\nscript\ntype\n=\n\"text/javascript\"\nsrc\n=\n\"http://localhost:8888/openbis/resources/api/v3/config.js\"\n></\nscript\n>\n<\nscript\ntype\n=\n\"text/javascript\"\nsrc\n=\n\"http://localhost:8888/openbis/resources/api/v3/require.js\"\n></\nscript\n>\n</\nhead\n>\n<\nbody\n>\n<\nscript\n>\n// With \"require\" call we asynchronously load \"openbis\", \"SpaceSearchCriteria\" and \"SpaceFetchOptions\" classes that we will need for our example.\n// The function that is passed as a second parameter of the require call is a callback that gets executed once requested classes are loaded.\n// In Javascript we work with exactly the same classes as in Java. For instance, \"ch.ethz.sis.openbis.generic.asapi.v3.dto.space.search.SpaceSearchCriteria\"\n// Java class and \"as/dto/space/search/SpaceSearchCriteria\" Javascript class have exactly the same methods. In order to find a Javascript class name please\n// check our Javadoc (https://openbis.ch/javadoc/20.10.x/javadoc-api-v3/index.html). The Javascript class name is defined in @JsonObject annotation of each V3 API Java DTO.\nrequire\n([\n\"openbis\"\n,\n\"as/dto/space/search/SpaceSearchCriteria\"\n,\n\"as/dto/space/fetchoptions/SpaceFetchOptions\"\n],\nfunction\n(\nopenbis\n,\nSpaceSearchCriteria\n,\nSpaceFetchOptions\n)\n{\n// get a reference to AS API\nvar\nv3\n=\nnew\nopenbis\n();\n// login to obtain a session token (the token it is automatically stored in openbis object and will be used for all subsequent API calls)\nv3\n.\nlogin\n(\n\"admin\"\n,\n\"password\"\n).\ndone\n(\nfunction\n()\n{\n// invoke other API methods, for instance search for spaces\nv3\n.\nsearchSpaces\n(\nnew\nSpaceSearchCriteria\n(),\nnew\nSpaceFetchOptions\n()).\ndone\n(\nfunction\n(\nresult\n)\n{\nalert\n(\n\"Number of spaces: \"\n+\nresult\n.\ngetObjects\n().\nlength\n);\n// logout to release the resources related with the session\nv3\n.\nlogout\n();\n});\n});\n});\n</\nscript\n>\n</\nbody\n>\n</\nhtml\n>\nAMD / RequireJS bundle\n\nTo improve the performance of AMD / RequireJS approach, we started to also provide a bundled version of the “config.js” called “config.bundle.js”\n(found in the same folder).\nUsing the bundled version of the config makes RequireJS issue only one request to the server to load all DTOs at once instead of separate requests for\neach DTO. This will significantly reduce the loading times of your webapp. What is also really nice is the fact it is so easy to start using this\nimprovement. Just load “config.bundle.js” instead of “config.js” and that’s it!\nEven though AMD / RequireJS solution is not recommended anymore, if you have a lot of existing code written with AMD / RequireJS approach then it\nmakes perfect sense to use this improvement before migrating to either VAR or ESM.\nVAR bundle\n\nVAR bundle (bundle assigned to window.openbis variable) allows you to overcome the shortcomings of AMD / RequireJS solution. First, the VAR bundle\nconsists of V3 API facade and all V3 API classes. Therefore, once the bundle is loaded, no further calls to the server are needed. Second, the bundle\nexposes the V3 API classes both via their simple names and their full names (see code example below) which makes it far easier for developers to use.\nThird, it does not require any additional library.\nVAR bundle can be loaded at an HTML page using a standard script tag.\nV3ConnectionExampleUsingVARBundle.html\n<!DOCTYPE html>\n<\nhtml\n>\n<\nhead\n>\n<\nmeta\ncharset\n=\n\"utf-8\"\n/>\n<\ntitle\n>\nV3ConnectionExampleUsingVARBundle\n</\ntitle\n>\n<!--\nImport VAR openBIS V3 API Javascript bundle as \"openbis\".\nThe bundle contains V3 API Javascript facade and all V3 API Javascript classes.", "heading": "AMD / RequireJS", "level": 3, "chunk_index": 16, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_17", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "The classes can be accessed via:\n\n- simple name (e.g. var space = new openbis.Space()) - works for classes with a unique simple name (see details below)\n- full name (e.g. var space = new opebis.as.dto.space.Space()) - works for all classes\nClasses with a unique simple name (e.g. Space) can be accessed using both their simple name (e.g. openbis.Space)\nand their full name (e.g. openbis.as.dto.space.Space).\nClasses with a non-unique simple name (e.g. ExternalDmsSearchCriteria) can be accessed only using their full name\n(i.e. as.dto.dataset.search.ExternalDmsSearchCriteria and as.dto.externaldms.search.ExternalDmsSearchCriteria).\nList of classes with duplicated simple names (i.e. accessible only via their full names):\n- as.dto.dataset.search.ExternalDmsSearchCriteria\n- as.dto.externaldms.search.ExternalDmsSearchCriteria\n- as.dto.pat.search.PersonalAccessTokenSessionNameSearchCriteria\n- as.dto.session.search.PersonalAccessTokenSessionNameSearchCriteria\n-->\n<!-- Import the bundle as \"openbis\" (the bundle content is assigned to window.openbis field).\nIn case window.openbis field is already used to store something different, then please\ncall openbis.noConflict() function right after the VAR bundle is loaded. It will bring back\nthe original value of window.openbis field and return the loaded VAR bundle for it to be\nassigned to a different field (works similar to jquery.noConflict() function).\n-->\n<\nscript\nsrc\n=\n\"http://localhost:8888/openbis/resources/api/v3/openbis.var.js\"\n></\nscript\n>\n</\nhead\n>\n<\nbody\n>\n<\nscript\n>\n// create an instance of the Javascript facade\nvar\nv3\n=\nnew\nopenbis\n.\nopenbis\n();\n// login to obtain a session token (the token it is automatically stored in openbis object and will be used for all subsequent API calls)\nv3\n.\nlogin\n(\n\"admin\"\n,\n\"admin\"\n).\ndone\n(\nfunction\n()\n{\n// invoke other API methods, for instance search for spaces\nv3\n.\nsearchSpaces\n(\nnew\nopenbis\n.\nSpaceSearchCriteria\n(),\nnew\nopenbis\n.\nSpaceFetchOptions\n()).\ndone\n(\nfunction\n(\nresult\n)\n{\nalert\n(\n\"Number of spaces: \"\n+\nresult\n.\ngetObjects\n().\nlength\n);\n// logout to release the resources related with the session\nv3\n.\nlogout\n();\n});\n});\n</\nscript\n>\n</\nbody\n>\n</\nhtml\n>\nESM bundle\n\nSimilar to VAR bundle, ESM bundle (ECMAScript module) is a bundle that contains the V3 API facade and all V3 API classes. It also exposes the V3 API\nclasses via both their simple names and their full names. The main difference between VAR and ESM is the format of the bundle and how and where it can\nbe imported.\nESM bundle can be loaded at an HTML page using a standard script tag with type=”module”. It is also well suited for webapps that bundle all their\nresources with tools like Webpack.\nV3ConnectionExampleUsingESMBundle.html\n<!DOCTYPE html>\n<\nhtml\n>\n<\nhead\n>\n<\nmeta\ncharset\n=\n\"utf-8\"\n/>\n<\ntitle\n>\nV3ConnectionExampleUsingESMBundle\n</\ntitle\n>\n</\nhead\n>\n<\nbody\n>\n<!--\nImport ESM (ECMAScript module) openBIS V3 API Javascript bundle as \"openbis\".\nThe bundle contains V3 API Javascript facade and all V3 API Javascript classes.", "heading": "The classes can be accessed via:", "level": 3, "chunk_index": 17, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_18", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "The classes can be accessed via:\n\n- simple name (e.g. var space = new openbis.Space()) - works for classes with a unique simple name (see details below)\n- full name (e.g. var space = new opebis.as.dto.space.Space()) - works for all classes\nClasses with a unique simple name (e.g. Space) can be accessed using both their simple name (e.g. openbis.Space)\nand their full name (e.g. openbis.as.dto.space.Space).\nClasses with a non-unique simple name (e.g. ExternalDmsSearchCriteria) can be accessed only using their full name\n(i.e. as.dto.dataset.search.ExternalDmsSearchCriteria and as.dto.externaldms.search.ExternalDmsSearchCriteria).\nList of classes with duplicated simple names (i.e. accessible only via their full names):\n- as.dto.dataset.search.ExternalDmsSearchCriteria\n- as.dto.externaldms.search.ExternalDmsSearchCriteria\n- as.dto.pat.search.PersonalAccessTokenSessionNameSearchCriteria\n- as.dto.session.search.PersonalAccessTokenSessionNameSearchCriteria\n-->\n<\nscript\ntype\n=\n\"module\"\n>\n// import the bundle as \"openbis\" (the bundle can be imported with a different name)\nimport\nopenbis\nfrom\n\"http://localhost:8888/openbis/resources/api/v3/openbis.esm.js\"\n// create an instance of the Javascript facade\nvar\nv3\n=\nnew\nopenbis\n.\nopenbis\n();\n// login to obtain a session token (the token it is automatically stored in openbis object and will be used for all subsequent API calls)\nv3\n.\nlogin\n(\n\"admin\"\n,\n\"admin\"\n).\ndone\n(\nfunction\n()\n{\n// invoke other API methods, for instance search for spaces\nv3\n.\nsearchSpaces\n(\nnew\nopenbis\n.\nSpaceSearchCriteria\n(),\nnew\nopenbis\n.\nSpaceFetchOptions\n()).\ndone\n(\nfunction\n(\nresult\n)\n{\nalert\n(\n\"Number of spaces: \"\n+\nresult\n.\ngetObjects\n().\nlength\n);\n// logout to release the resources related with the session\nv3\n.\nlogout\n();\n});\n});\n</\nscript\n>\n</\nbody\n>\n</\nhtml\n>", "heading": "The classes can be accessed via:", "level": 3, "chunk_index": 18, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_19", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Synchronous Java vs Asynchronous Javascript\n\n\nEven though the V3 API code examples in both Java and Javascript look similar there is one major difference between them. All the methods in the Java\nAPI that connect to the openBIS server are synchronous, while all their Javascript counterparts are asynchronous. Let’s compare how that looks.\nV3JavaCallsAreSynchronous.java\npublic\nclass\nV3JavaCallsAreSynchronous\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created (please check \"Accessing the API\" section for more details)\n// this makes a synchronous (blocking) call to the server\nSearchResult\n<\nSpace\n>\nspaces\n=\nv3\n.\nsearchSpaces\n(\nnew\nSpaceSearchCriteria\n(),\nnew\nSpaceFetchOptions\n());\n// this loop will execute only after searchSpaces method call is finished and spaces have been fetched from the server\nfor\n(\nSpace\nspace", "heading": "Synchronous Java vs Asynchronous Javascript", "level": 3, "chunk_index": 19, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_20", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nspaces\n)\n{\nSystem\n.\nout\n.\nprintln\n(\nspace\n.\ngetCode\n());\n}\n}\n}\nV3JavascriptCallsAreAsynchronous.java\n<!DOCTYPE html>\n<\nhtml\n>\n<\nhead\n>\n<\nmeta\ncharset\n=\n\"utf-8\"\n/>\n<\ntitle\n>\nV3JavascriptCallsAreAsynchronous\n</\ntitle\n>\n</\nhead\n>\n<\nbody\n>\n<\nscript\n>\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// this makes a non-blocking (asynchronous) call to the server\nv3\n.\nsearchSpaces\n(\nnew\nSpaceSearchCriteria\n(),\nnew\nSpaceFetchOptions\n()).\ndone\n(\n// we need to put the loop in \"done\" callback for it to be executed once spaces have been fetched from the server\nfunction\n(\nresult\n)\n{\nresult\n.\ngetObjects\n().\nforEach\n(\nfunction\n(\nspace\n){\nconsole\n.\nlog\n(\nspace\n.\ngetCode\n());\n});\n}\n);\n</\nscript\n>\n</\nbody\n>\n</\nhtml\n>\nWhat Javascript V3 API asynchronous functions actually return is a jQuery Promise object, which offers methods like: then, done, fail (\nsee: https://api.jquery.com/category/deferred-object/). These methods can be used for registering different callbacks that will be either executed\nwhen a call succeeds or fails (e.g. due to a network problem).\nA more modern and an easier to understand way of working with Promise objects is the async / await syntax (\nsee: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/async_function). Our asynchronous Javascript V3 API methods support\nit as well.", "heading": ":", "level": 3, "chunk_index": 20, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_21", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "IV. AS Methods\n\n\nThe sections below describe how to use different methods of the V3 API. Each section describes a group of similar methods. For instance, we have one\nsection that describes creation of entities. Even though the API provides us methods for creation of spaces, projects, experiments, samples and\nmaterials, vocabulary terms, tags we only concentrate here on creation of samples. Samples are the most complex entity kind. Once you understand how\ncreation of samples works you will also know how to create other kinds of entities as all creation methods follow the same patterns. The same applies\nfor other methods like updating of entities, searching or getting entities. We will introduce them using the sample example.\nEach section will be split into Java and Javascript subsections. We want to keep Java and Javascript code examples close to each other so that you can\neasily see what are the similarities and differences in the API usage between these two languages.\nNOTE: The following code examples assume that we have already got a reference to the V3 API and we have already authenticated to get a session token.\nMoreover in Javascript example we do not include the html page template to make them shorter and more readable. Please check “Accessing the API”\nsection for examples on how to get a reference to V3 API, authenticate or build a simple html page.\nLogin\n", "heading": "IV. AS Methods", "level": 3, "chunk_index": 21, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_22", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "OpenBIS provides the following login methods:\n\nlogin(user, password) - login as a given user\nloginAs(user, password, asUser) - login on behalf of a different\nuser (e.g. I am an admin but I would like to see only things user\n“x” would normally see)\nloginAsAnonymousUser() - login as an anonymous user configured in AS\nservice.properties\nAll login methods return a session token if the provided parameters were\ncorrect. In case a given user does not exist or the provided password\nwas incorrect the login methods return null.\nExample\n\nV3LoginExample.java\npublic\nclass\nV3LoginExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created (please check \"Accessing the API\" section for more details)\n// login as a specific user\nString\nsessionToken\n=\nv3\n.\nlogin\n(\n\"admin\"\n,\n\"password\"\n);\nSystem\n.\nout\n.\nprintln\n(\nsessionToken\n);\n// login on behalf of a different user (I am an admin but I would like to see only things that some other user would normally see)\nsessionToken\n=\nv3\n.\nloginAs\n(\n\"admin\"\n,\n\"password\"\n,\n\"someotheruser\"\n);\nSystem\n.\nout\n.\nprintln\n(\nsessionToken\n);\n// login as an anonymous user (anonymous user has to be configured in service.properties first)\nsessionToken\n=\nv3\n.\nloginAsAnonymousUser\n();\nSystem\n.\nout\n.\nprintln\n(\nsessionToken\n);\n}\n}\nV3LoginExample.html\n<\nscript\n>\n// we assume here that v3 object has been already created (please check \"Accessing the API\" section for more details)\n// login as a specific user\nv3\n.\nlogin\n(\n\"admin\"\n,\n\"password\"\n).\ndone\n(\nfunction\n(\nsessionToken\n)\n{\nalert\n(\nsessionToken\n);\n// login on behalf of a different user (I am an admin but I would like to see only things that some other user would normally see)\nv3\n.\nloginAs\n(\n\"admin\"\n,\n\"password\"\n,\n\"someotheruser\"\n).\ndone\n(\nfunction\n(\nsessionToken\n)\n{\nalert\n(\nsessionToken\n);\n// login as an anonymous user (anonymous user has to be configured in service.properties first)\nv3\n.\nloginAsAnonymousUser\n().\ndone\n(\nfunction\n(\nsessionToken\n)\n{\nalert\n(\nsessionToken\n);\n});\n});\n});\n</\nscript\n>", "heading": "OpenBIS provides the following login methods:", "level": 3, "chunk_index": 22, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_23", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Personal Access Tokens\n\n\nA personal access token (in short: PAT) can be thought of as a longer lived session token which can be used for integrating openBIS with external systems. If you would like to learn more about the idea behind PATs please read:", "heading": "Personal Access Tokens", "level": 3, "chunk_index": 23, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_24", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Personal Access Tokens\n\n).\nExample of how to create and use a PAT:\nimport\njava.util.Arrays\n;\nimport\njava.util.Date\n;\nimport\njava.util.List\n;\nimport\njava.util.Map\n;\nimport\norg.apache.commons.lang.time.DateUtils\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.pat.PersonalAccessToken\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.pat.create.PersonalAccessTokenCreation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.pat.fetchoptions.PersonalAccessTokenFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.pat.id.IPersonalAccessTokenId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.pat.id.PersonalAccessTokenPermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.fetchoptions.SpaceFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.search.SpaceSearchCriteria\n;\npublic\nclass\nV3PersonalAccessTokenExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nPersonalAccessTokenCreation\ncreation\n=\nnew\nPersonalAccessTokenCreation\n();\ncreation\n.\nsetSessionName\n(\n\"test session\"\n);\ncreation\n.\nsetValidFromDate\n(\nnew\nDate\n(\nSystem\n.\ncurrentTimeMillis\n()\n-\nDateUtils\n.", "heading": "Personal Access Tokens", "level": 3, "chunk_index": 24, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_25", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "MILLIS_PER_DAY\n\n));\ncreation\n.\nsetValidToDate\n(\nnew\nDate\n(\nSystem\n.\ncurrentTimeMillis\n()\n+\nDateUtils\n.", "heading": "MILLIS_PER_DAY", "level": 2, "chunk_index": 25, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_26", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "MILLIS_PER_DAY\n\n));\n// create and get the new PAT\nList\n<\nPersonalAccessTokenPermId\n>\nids\n=\nv3api\n.\ncreatePersonalAccessTokens\n(\nsessionToken\n,\nArrays\n.\nasList\n(\ncreation\n));\nMap\n<\nIPersonalAccessTokenId\n,\nPersonalAccessToken\n>\nmap\n=\nv3api\n.\ngetPersonalAccessTokens\n(\nsessionToken\n,\nids\n,\nnew\nPersonalAccessTokenFetchOptions\n());\nPersonalAccessToken\npat\n=\nmap\n.\nget\n(\nids\n.\nget\n(\n0\n));\n// use the new PAT to list spaces\nv3api\n.\nsearchSpaces\n(\npat\n.\ngetHash\n(),\nnew\nSpaceSearchCriteria\n(),\nnew\nSpaceFetchOptions\n());\n}\n}", "heading": "MILLIS_PER_DAY", "level": 2, "chunk_index": 26, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_27", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "already log in user:\n\nExample\n\nV3CreationExample.java\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.session.SessionInformation\n;\npublic\nclass\nV3SessionInformationExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSessionInformation\nsessionInformation\n=\nv3\n.\ngetSessionInformation\n(\nsessionToken\n);\nSystem\n.\nout\n.\nprintln\n(\n\"User Name: \"\n+\nsessionInformation\n.\ngetUserName\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Home Group: \"\n+\nsessionInformation\n.\ngetHomeGroupCode\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Person: \"\n+\nsessionInformation\n.\ngetPerson\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Creator Person: \"\n+\nsessionInformation\n.\ngetCreatorPerson\n());\n}\n}\nCreating entities\n\nThe methods for creating entities in V3 API are called: createSpaces,\ncreateProjects, createExperiments, createSamples, createMaterials,\ncreateVocabularyTerms, createTags. They all allow to create one or more\nentities at once by passing one or more entity creation objects (i.e.", "heading": "already log in user:", "level": 3, "chunk_index": 27, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_28", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "MaterialCreation, VocabularyTermCreation, TagCreation). All these\n\nmethods return as a result a list of the new created entity perm ids.\nNOTE: Creating data sets via V3 API is not available yet. The new V3\ndropboxes are planned but not implemented yet. Please use V2 dropboxes\nuntil V3 version is out.\nExample\n\nV3CreationExample.java\nimport\njava.util.List\n;\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.entitytype.id.EntityTypePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.experiment.id.ExperimentIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.SampleCreation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.id.SamplePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.id.SpacePermId\n;\npublic\nclass\nV3CreationExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleCreation\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "MaterialCreation, VocabularyTermCreation, TagCreation). All these", "level": 3, "chunk_index": 28, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_29", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\n// you can also pass more than one creation object to create multiple entities at once\nList\n<\nSamplePermId\n>\npermIds\n=\nv3\n.\ncreateSamples\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nsample\n));\nSystem\n.\nout\n.\nprintln\n(\n\"Perm ids: \"\n+\npermIds\n);\n}\n}\nV3CreationExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/create/SampleCreation\"\n,\n\"as/dto/entitytype/id/EntityTypePermId\"\n,\n\"as/dto/space/id/SpacePermId\"\n,\n\"as/dto/experiment/id/ExperimentIdentifier\"\n],\nfunction\n(\nSampleCreation\n,\nEntityTypePermId\n,\nSpacePermId\n,\nExperimentIdentifier\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 29, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_30", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\n// you can also pass more than one creation object to create multiple entities at once\nv3\n.\ncreateSamples\n([\nsample\n]).\ndone\n(\nfunction\n(\npermIds\n)\n{\nalert\n(\n\"Perm ids: \"\n+", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 30, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_31", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "JSON\n\n.\nstringify\n(\npermIds\n));\n});\n});\n</\nscript\n>\nProperties example\n\nV3CreationWithPropertiesExample.java\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.entitytype.id.EntityTypePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.experiment.id.ExperimentIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.SampleCreation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.id.SpacePermId\n;\npublic\nclass\nV3CreationWithPropertiesExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleCreation\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "JSON", "level": 2, "chunk_index": 31, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_32", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\n// examples of value formats that should be used for different types of properties\nsample\n.\nsetProperty\n(", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 32, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_33", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_TERM_CODE\"\n\n);\nv3\n.\ncreateSamples\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nsample\n));\n}\n}\nV3CreationWithPropertiesExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/create/SampleCreation\"\n,\n\"as/dto/entitytype/id/EntityTypePermId\"\n,\n\"as/dto/space/id/SpacePermId\"\n,\n\"as/dto/experiment/id/ExperimentIdentifier\"\n],\nfunction\n(\nSampleCreation\n,\nEntityTypePermId\n,\nSpacePermId\n,\nExperimentIdentifier\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_TERM_CODE\"", "level": 2, "chunk_index": 33, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_34", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\n// examples of value formats that should be used for different types of properties\nsample\n.\nsetProperty\n(", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 34, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_35", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_TERM_CODE\"\n\n);\nv3\n.\ncreateSamples\n([\nsample\n]).\ndone\n(\nfunction\n(\npermIds\n)\n{\nalert\n(\n\"Perm ids: \"\n+", "heading": "\"MY_TERM_CODE\"", "level": 2, "chunk_index": 35, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_36", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "JSON\n\n.\nstringify\n(\npermIds\n));\n});\n});\n});\n</\nscript\n>\nDifferent ids example\n\nV3CreationWithDifferentIdsExample.java\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.entitytype.id.EntityTypePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.experiment.id.ExperimentIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.experiment.id.ExperimentPermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.SampleCreation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.id.SpacePermId\n;\npublic\nclass\nV3CreationWithDifferentIdsExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleCreation\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "JSON", "level": 2, "chunk_index": 36, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_37", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\n// as an experiment id we can use any class that implements IExperimentId interface. For instance, experiment identifier:\nsample\n.\nsetExperimentId\n(\nnew\nExperimentIdentifier\n(", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 37, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_38", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "// or experiment perm id:\n\nsample\n.\nsetExperimentId\n(\nnew\nExperimentPermId\n(\n\"20160115170718361-98668\"\n));\nv3\n.\ncreateSamples\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nsample\n));\n}\n}\nV3CreationWithDifferentIdsExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/create/SampleCreation\"\n,\n\"as/dto/entitytype/id/EntityTypePermId\"\n,\n\"as/dto/space/id/SpacePermId\"\n,\n\"as/dto/experiment/id/ExperimentIdentifier\"\n,\n\"as/dto/experiment/id/ExperimentPermId\"\n],\nfunction\n(\nSampleCreation\n,\nEntityTypePermId\n,\nSpacePermId\n,\nExperimentIdentifier\n,\nExperimentPermId\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "// or experiment perm id:", "level": 3, "chunk_index": 38, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_39", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\n// as an experiment id we can use any class that implements IExperimentId interface. For instance, experiment identifier:\nsample\n.\nsetExperimentId\n(\nnew\nExperimentIdentifier\n(", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 39, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_40", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "// or experiment perm id:\n\nsample\n.\nsetExperimentId\n(\nnew\nExperimentPermId\n(\n\"20160115170718361-98668\"\n));\nv3\n.\ncreateSamples\n([\nsample\n]).\ndone\n(\nfunction\n(\npermIds\n)\n{\nalert\n(\n\"Perm ids: \"\n+", "heading": "// or experiment perm id:", "level": 3, "chunk_index": 40, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_41", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "JSON\n\n.\nstringify\n(\npermIds\n));\n});\n});\n</\nscript\n>\nParent child example\n\nThe following example creates parent and child samples for a sample type", "heading": "JSON", "level": 2, "chunk_index": 41, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_42", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "which allow automatic code generation:\n\nV3CreationParentAndChildExample\nimport\njava.util.Arrays\n;\nimport\njava.util.List\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.id.CreationId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.entitytype.id.EntityTypePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.SampleCreation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.id.SamplePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.id.SpacePermId\n;\npublic\nclass\nV3CreationParentAndChildExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleCreation\nparentSample\n=\nnew\nSampleCreation\n();\nparentSample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "which allow automatic code generation:", "level": 3, "chunk_index": 42, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_43", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SPACE_CODE\"\n\n));\nparentSample\n.\nsetCreationId\n(\nnew\nCreationId\n(\n\"parent\"\n));\nSampleCreation\nchildSample\n=\nnew\nSampleCreation\n();\nchildSample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_SPACE_CODE\"", "level": 2, "chunk_index": 43, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_44", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SPACE_CODE\"\n\n));\nchildSample\n.\nsetParentIds\n(\nArrays\n.\nasList\n(\nparentSample\n.\ngetCreationId\n()));\nList\n<\nSamplePermId\n>\npermIds\n=\nv3\n.\ncreateSamples\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nparentSample\n,\nchildSample\n));\nSystem\n.\nout\n.\nprintln\n(\n\"Perm ids: \"\n+\npermIds\n);\n}\n}\nV3CreationParentAndChildExample.html\n<\nscript\n>\nrequire\n([\n\"openbis\"\n,\n\"as/dto/sample/create/SampleCreation\"\n,\n\"as/dto/entitytype/id/EntityTypePermId\"\n,\n\"as/dto/space/id/SpacePermId\"\n,\n\"as/dto/common/id/CreationId\"\n],\nfunction\n(\nopenbis\n,\nSampleCreation\n,\nEntityTypePermId\n,\nSpacePermId\n,\nCreationId\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nparentSample\n=\nnew\nSampleCreation\n();\nparentSample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_SPACE_CODE\"", "level": 2, "chunk_index": 44, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_45", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SPACE_CODE\"\n\n));\nparentSample\n.\nsetCreationId\n(\nnew\nCreationId\n(\n\"parent\"\n));\nvar\nchildSample\n=\nnew\nSampleCreation\n();\nchildSample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_SPACE_CODE\"", "level": 2, "chunk_index": 45, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_46", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SPACE_CODE\"\n\n));\nchildSample\n.\nsetParentIds\n([\nparentSample\n.\ngetCreationId\n()]);\nv3\n.\ncreateSamples\n([\nparentSample\n,\nchildSample\n]).\ndone\n(\nfunction\n(\npermIds\n)\n{\nalert\n(\n\"Perm ids: \"\n+", "heading": "\"MY_SPACE_CODE\"", "level": 2, "chunk_index": 46, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_47", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "JSON\n\n.\nstringify\n(\npermIds\n));\n});\n});\n</\nscript\n>\nUpdating entities\n\nThe methods for updating entities in V3 API are called: updateSpaces,\nupdateProjects, updateExperiments, updateSamples, updateDataSets,\nupdateMaterials, updateVocabularyTerms, updateTags. They all allow to\nupdate one or more entities at once by passing one or more entity update\nobjects (i.e. SpaceUpdate, ProjectUpdate, ExperimentUpdate,", "heading": "JSON", "level": 2, "chunk_index": 47, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_48", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SampleUpdate, MaterialUpdate, VocabularyTermUpdate, TagUpdate). With\n\nupdate objects you can update entities without fetching their state\nfirst, i.e. the update objects contain only changes - not the full state\nof entities. All update objects require an id of an entity that will be\nupdated. Please note that some of the entity fields cannot be changed\nonce an entity is created, for instance sample code becomes immutable\nafter creation.\nExample\n\nV3UpdateExample.java\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.experiment.id.ExperimentIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.id.SampleIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.update.SampleUpdate\n;\npublic\nclass\nV3UpdateExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// here we update a sample and attach it to a different experiment\nSampleUpdate\nsample\n=\nnew\nSampleUpdate\n();\nsample\n.\nsetSampleId\n(\nnew\nSampleIdentifier\n(", "heading": "SampleUpdate, MaterialUpdate, VocabularyTermUpdate, TagUpdate). With", "level": 3, "chunk_index": 48, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_49", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"/MY_SPACE_CODE/MY_PROJECT_CODE/MY_OTHER_EXPERIMENT_CODE\"\n\n));\n// you can also pass more than one update object to update multiple entities at once\nv3\n.\nupdateSamples\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nsample\n));\nSystem\n.\nout\n.\nprintln\n(\n\"Updated\"\n);\n}\n}\nV3UpdateExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/update/SampleUpdate\"\n,\n\"as/dto/sample/id/SampleIdentifier\"\n,\n\"as/dto/experiment/id/ExperimentIdentifier\"\n],\nfunction\n(\nSampleUpdate\n,\nSampleIdentifier\n,\nExperimentIdentifier\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// here we update a sample and attach it to a different experiment\nvar\nsample\n=\nnew\nSampleUpdate\n();\nsample\n.\nsetSampleId\n(\nnew\nSampleIdentifier\n(", "heading": "\"/MY_SPACE_CODE/MY_PROJECT_CODE/MY_OTHER_EXPERIMENT_CODE\"", "level": 2, "chunk_index": 49, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_50", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"/MY_SPACE_CODE/MY_PROJECT_CODE/MY_OTHER_EXPERIMENT_CODE\"\n\n));\n// you can also pass more than one update object to update multiple entities at once\nv3\n.\nupdateSamples\n([\nsample\n]).\ndone\n(\nfunction\n()\n{\nalert\n(\n\"Updated\"\n);\n});\n});\n</\nscript\n>\nProperties example\n\nV3UpdateWithPropertiesExample.java\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.id.SampleIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.update.SampleUpdate\n;\npublic\nclass\nV3UpdateWithPropertiesExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleUpdate\nsample\n=\nnew\nSampleUpdate\n();\nsample\n.\nsetSampleId\n(\nnew\nSampleIdentifier\n(", "heading": "\"/MY_SPACE_CODE/MY_PROJECT_CODE/MY_OTHER_EXPERIMENT_CODE\"", "level": 2, "chunk_index": 50, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_51", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE\"\n\n));\n// examples of value formats that should be used for different types of properties\nsample\n.\nsetProperty\n(", "heading": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 51, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_52", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_TERM_CODE\"\n\n);\nv3\n.\nupdateSamples\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nsample\n));\nSystem\n.\nout\n.\nprintln\n(\n\"Updated\"\n);\n}\n}\nV3UpdateWithPropertiesExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/update/SampleUpdate\"\n,\n\"as/dto/sample/id/SampleIdentifier\"\n],\nfunction\n(\nSampleUpdate\n,\nSampleIdentifier\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nsample\n=\nnew\nSampleUpdate\n();\nsample\n.\nsetSampleId\n(\nnew\nSampleIdentifier\n(", "heading": "\"MY_TERM_CODE\"", "level": 2, "chunk_index": 52, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_53", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE\"\n\n));\n// examples of value formats that should be used for different types of properties\nsample\n.\nsetProperty\n(", "heading": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 53, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_54", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_TERM_CODE\"\n\n);\nv3\n.\nupdateSamples\n([\nsample\n]).\ndone\n(\nfunction\n()\n{\nalert\n(\n\"Updated\"\n);\n});\n});\n</\nscript\n>\nParents example\n\nV3UpdateWithParentsExample.java\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.id.SampleIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.update.SampleUpdate\n;\npublic\nclass\nV3UpdateWithParentsExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// Let's assume the sample we are about to update has the following parents:", "heading": "\"MY_TERM_CODE\"", "level": 2, "chunk_index": 54, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_55", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "// - MY_PARENT_CODE_2\n\nSampleUpdate\nsample\n=\nnew\nSampleUpdate\n();\nsample\n.\nsetSampleId\n(\nnew\nSampleIdentifier\n(", "heading": "// - MY_PARENT_CODE_2", "level": 2, "chunk_index": 55, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_56", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE\"\n\n));\n// We can add and remove parents from the existing list. For instance, here we are adding: MY_PARENT_CODE_3 and removing: MY_PARENT_CODE_1.\n// The list of parents after such change would be: [MY_PARENT_CODE_2, MY_PARENT_CODE_3]. Please note that we don't have to fetch the existing\n// list of parents, we are just defining what changes should be made to this list on the server side. Updating lists of children or contained\n// samples works exactly the same.\nsample\n.\ngetParentIds\n().\nadd\n(\nnew\nSampleIdentifier\n(", "heading": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 56, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_57", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"/MY_SPACE_CODE/MY_PARENT_CODE_1\"\n\n));\n// Instead of adding and removing parents we can also set the list of parents to a completely new value.\nsample\n.\ngetParentIds\n().\nset\n(\nnew\nSampleIdentifier\n(", "heading": "\"/MY_SPACE_CODE/MY_PARENT_CODE_1\"", "level": 2, "chunk_index": 57, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_58", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"/MY_SPACE_CODE/MY_PARENT_CODE_3\"\n\n));\nv3\n.\nupdateSamples\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nsample\n));\nSystem\n.\nout\n.\nprintln\n(\n\"Updated\"\n);\n}\n}\nV3UpdateWithParentsExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/update/SampleUpdate\"\n,\n\"as/dto/sample/id/SampleIdentifier\"\n],\nfunction\n(\nSampleUpdate\n,\nSampleIdentifier\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// Let's assume the sample we are about to update has the following parents:", "heading": "\"/MY_SPACE_CODE/MY_PARENT_CODE_3\"", "level": 2, "chunk_index": 58, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_59", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "// - MY_PARENT_CODE_2\n\nvar\nsample\n=\nnew\nSampleUpdate\n();\nsample\n.\nsetSampleId\n(\nnew\nSampleIdentifier\n(", "heading": "// - MY_PARENT_CODE_2", "level": 2, "chunk_index": 59, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_60", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE\"\n\n));\n// We can add and remove parents from the existing list. For instance, here we are adding: MY_PARENT_CODE_3 and removing: MY_PARENT_CODE_1.\n// The list of parents after such change would be: [MY_PARENT_CODE_2, MY_PARENT_CODE_3]. Please note that we don't have to fetch the existing\n// list of parents, we are just defining what changes should be made to this list on the server side. Updating lists of children or contained\n// samples works exactly the same.\nsample\n.\ngetParentIds\n().\nadd\n(\nnew\nSampleIdentifier\n(", "heading": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 60, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_61", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"/MY_SPACE/MY_PARENT_CODE_1\"\n\n));\n// Instead of adding and removing parents we can also set the list of parents to a completely new value.\nsample\n.\ngetParentIds\n().\nset\n(\nnew\nSampleIdentifier\n(", "heading": "\"/MY_SPACE/MY_PARENT_CODE_1\"", "level": 2, "chunk_index": 61, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_62", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SPACE/MY_PARENT_CODE_3\"\n\n));\nv3\n.\nupdateSamples\n([\nsample\n]).\ndone\n(\nfunction\n()\n{\nalert\n(\n\"Updated\"\n);\n});\n});\n</\nscript\n>\nGetting authorization rights for entities\n\nIf the user isn’t allowed to create or update an entity an exception is\nthrown. But often a client application wants to know in advance whether\nsuch operations are allowed or not. With the API method\ngetRights()\nauthorizations rights for specified entities can be requested. Currently\nonly creation and update authorization rights for projects, experiments,\nsamples and data sets (only update right) are returned.\nIn order to check whether an entity can be created or not a dummy\nidentifier has to be provided when calling\ngetRights()\n. This\nidentifier should be a wellformed identifier which specifies the entity\nto which such a new entity belongs. For example, calling\ngetRights()\nwith\nnew\nExperimentIdentifier(\"/MY-SPACE/PROJECT1/DUMMY\")\nwould return\nrights containing", "heading": "\"MY_SPACE/MY_PARENT_CODE_3\"", "level": 2, "chunk_index": 62, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_63", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "/MY-SPACE/PROJECT1\n\n.\nFreezing entities\n\nAn entity (Space, Project, Experiment, Sample, Data Set) can be frozen.", "heading": "/MY-SPACE/PROJECT1", "level": 2, "chunk_index": 63, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_64", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "There are two types of frozen:\n\nCore\nand\nsurface\n. A frozen core means\nthat certain attributes of the entity can not be changed but still\nconnections between entities can be added or removed. A frozen surface\nimplies a frozen core and frozen connections of particular types. To\nfreeze an entity it has to be updated by invoking at least one freeze", "heading": "There are two types of frozen:", "level": 3, "chunk_index": 64, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_65", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "method on the update object. Example:\n\nSampleUpdate sample = new SampleUpdate();\nsample.setSampleId(new SampleIdentifier(\"/MY_SPACE_CODE/MY_SAMPLE_CODE\"));\nsample.freezeForChildren();\nv3.updateSamples(sessionToken, Arrays.asList(sample));\nWarning\nFreezing can not be reverted.\nThe timestamp of freezing, the types of freezing, the user and the\nidentifier of the frozen entity will be stored in the database as a\nfreezing event.\nThe following tables show all freezing possibilities and what is actual\nfrozen.\nSpace\n\nFreezing method\nDescription\nfreeze\nThe specified space can not be deleted.\nThe description can not be set or changed.\nfreezeForProjects\nSame as freeze() plus no projects can be added to or removed from the specified space.\nfreezeForSamples\nSame as freeze() plus no samples can be added to or removed from the specified space.\nProject\n\nFreezing method\nDescription\nfreeze\nThe specified project can not be deleted.\nThe description can not be set or changed.\nNo attachments can be added or removed.\nfreezeForExperiments\nSame as freeze() plus no experiments can be added to or removed from the specified project.\nfreezeForSamples\nSame as freeze() plus no samples can be added to or removed from the specified project.\nExperiment\n\nFreezing method\nDescription\nfreeze\nThe specified experiment can not be deleted.\nNo properties can be added, removed or modified.\nNo attachments can be added or removed.\nfreezeForSamples\nSame as freeze() plus no samples can be added to or removed from the specified experiment.\nfreezeForDataSets\nSame as freeze() plus no data sets can be added to or removed from the specified experiment.\nSample\n\nFreezing method\nDescription\nfreeze\nThe specified sample can not be deleted.\nNo properties can be added, removed or modified.\nNo attachments can be added or removed.\nfreezeForComponents\nSame as freeze() plus no component samples can be added to or removed from the specified sample.\nfreezeForChildren\nSame as freeze() plus no child samples can be added to or removed from the specified sample.\nfreezeForParents\nSame as freeze() plus no parent samples can be added to or removed from the specified sample.\nfreezeForDataSets\nSame as freeze() plus no data sets can be added to or removed from the specified sample.", "heading": "method on the update object. Example:", "level": 3, "chunk_index": 65, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_66", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Data Set\n\n\nFreezing method\nDescription\nfreeze\nThe specified data set can not be deleted.\nNo properties can be added, removed or modified.\nContent copies can be still added or removed for frozen link data sets.\nfreezeForChildren\nSame as freeze() plus no child data sets can be added to or removed from the specified data set.\nfreezeForParents\nSame as freeze() plus no parent data sets can be added to or removed from the specified data set.\nfreezeForComponents\nSame as freeze() plus no component data sets can be added to or removed from the specified data set.\nfreezeForContainers\nSame as freeze() plus no container data sets can be added to or removed from the specified data set.\nSearching entities\n\nThe methods for searching entities in V3 API are called:\nsearchSpaces\n,\nsearchProjects\n,\nsearchExperiments\n,\nsearchSamples\n,\nsearchDataSets\n,\nsearchMaterials\n,\nsearchVocabularyTerms,\nsearchTags\n,\nsearchGlobally\n.\nThey all take criteria and fetch options objects as an input. The\ncriteria object allows you to specify what entities you are looking for.\nFor instance, only entities from a given space, entities of a given\ntype, entities with a property X that equals Y and much much more.\nThe fetch options object allows you to tell the API which parts of the\nentities found should be fetched and returned as a result of the method\ncall. For instance, you can tell the API to return the results only with\nproperties because this is all what you will need for your processing.\nThis gives you a very fine grained control over how much data you\nactually fetch from the server. The less you ask for via fetch options\nthe less data the API has to load from the database and the less data it\nwill have to transfer over the network. Therefore by default, the fetch\noptions object is empty, i.e. it tells the API only to fetch the basic\ninformation about a given entity, i.e. its id, attributes and creation\nand registration dates. If you want to fetch anything more then you have\nto let the API know via fetch options which parts you are also\ninterested in.\nAnother functionality that the fetch options object provides is\npagination (see FetchOptions.from(Integer) and\nFetchOptions.count(Integer) methods). With pagination a user can control\nif a search method shall return all found results or just a given\nsubrange. This is especially useful for handling very large numbers of\nresults e.g. when we want to build a UI to present them. In such a\nsituation, we can perform the search that returns only the first batch\nof results (e.g. the first 100) for the UI to be responsive and ask for\nanother batch only if a user requests that (e.g. via clicking on a next\npage button in the UI). The pagination is available in all the search\nmethods including the global search (i.e. searchGlobally method). A code\nexample on how to use the pagination methods is presented below.\nApart from the pagination the fetch options also provides the means to\nsort the results (see FetchOptions.sortBy() method). What fields can be\nused for sorting depends on the search method and the returned objects.\nResults can be sorted ascending or descending. Sorting by multiple\nfields is also possible (e.g. first sort by type and then by\nidentifier). A code example on how to use sorting is presented below.\nExample\n\nV3SearchExample.java\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.Sample\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.fetchoptions.SampleFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.search.SampleSearchCriteria\n;\npublic\nclass\nV3SearchExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// search for samples that are in space with code MY_SPACE_CODE and are of sample type with code MY_SAMPLE_TYPE_CODE\nSampleSearchCriteria\ncriteria\n=\nnew\nSampleSearchCriteria\n();\ncriteria\n.\nwithSpace\n().\nwithCode\n().\nthatEquals\n(", "heading": "Data Set", "level": 3, "chunk_index": 66, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_67", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_TYPE_CODE\"\n\n);\n// tell the API to fetch properties for each returned sample\nSampleFetchOptions\nfetchOptions\n=\nnew\nSampleFetchOptions\n();\nfetchOptions\n.\nwithProperties\n();\nSearchResult\n<\nSample\n>\nresult\n=\nv3\n.\nsearchSamples\n(\nsessionToken\n,\ncriteria\n,\nfetchOptions\n);\nfor\n(\nSample\nsample", "heading": "\"MY_SAMPLE_TYPE_CODE\"", "level": 2, "chunk_index": 67, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_68", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nresult\n.\ngetObjects\n())\n{\n// because we asked for properties via fetch options we can access them here, otherwise NotFetchedException would be thrown by getProperties method\nSystem\n.\nout\n.\nprintln\n(\n\"Sample \"\n+\nsample\n.\ngetIdentifier\n()\n+\n\" has properties: \"\n+\nsample\n.\ngetProperties\n());\n}\n}\n}\nV3SearchExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/search/SampleSearchCriteria\"\n,\n\"as/dto/sample/fetchoptions/SampleFetchOptions\"\n],\nfunction\n(\nSampleSearchCriteria\n,\nSampleFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// search for samples that are in space with code MY_SPACE_CODE and are of sample type with code MY_SAMPLE_TYPE_CODE\nvar\ncriteria\n=\nnew\nSampleSearchCriteria\n();\ncriteria\n.\nwithSpace\n().\nwithCode\n().\nthatEquals\n(", "heading": ":", "level": 3, "chunk_index": 68, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_69", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_TYPE_CODE\"\n\n);\n// tell the API to fetch properties for each returned sample\nvar\nfetchOptions\n=\nnew\nSampleFetchOptions\n();\nfetchOptions\n.\nwithProperties\n();\nv3\n.\nsearchSamples\n(\ncriteria\n,\nfetchOptions\n).\ndone\n(\nfunction\n(\nresult\n)\n{\nresult\n.\ngetObjects\n().\nforEach\n(\nfunction\n(\nsample\n)\n{\n// because we asked for properties via fetch options we can access them here, otherwise NotFetchedException would be thrown by getProperties method\nalert\n(\n\"Sample \"\n+\nsample\n.\ngetIdentifier\n()\n+\n\" has properties: \"\n+", "heading": "\"MY_SAMPLE_TYPE_CODE\"", "level": 2, "chunk_index": 69, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_70", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Example with pagination and sorting\n\n\nV3SearchWithPaginationAndSortingExample.java\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.Sample\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.fetchoptions.SampleFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.search.SampleSearchCriteria\n;\npublic\nclass\nV3SearchWithPaginationAndSortingExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleSearchCriteria\ncriteria\n=\nnew\nSampleSearchCriteria\n();\nSampleFetchOptions\nfetchOptions\n=\nnew\nSampleFetchOptions\n();\n// get the first 100 results\nfetchOptions\n.\nfrom\n(\n0\n);\nfetchOptions\n.\ncount\n(\n100\n);\n// sort the results first by a type (ascending) and then by an identifier (descending)\nfetchOptions\n.\nsortBy\n().\ntype\n().\nasc\n();\nfetchOptions\n.\nsortBy\n().\nidentifier\n().\ndesc\n();\nSearchResult\n<\nSample\n>\nresult\n=\nv3\n.\nsearchSamples\n(\nsessionToken\n,\ncriteria\n,\nfetchOptions\n);\n// because of the pagination the list contains only the first 100 objects (or even less if there are fewer results found)\nSystem\n.\nout\n.\nprintln\n(\nresult\n.\ngetObjects\n());\n// returns the number of all found results (i.e. potentially more than 100)\nSystem\n.\nout\n.\nprintln\n(\nresult\n.\ngetTotalCount\n());\n}\n}\nV3SearchWithPaginationAndSortingExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/search/SampleSearchCriteria\"\n,\n\"as/dto/sample/fetchoptions/SampleFetchOptions\"\n],\nfunction\n(\nSampleSearchCriteria\n,\nSampleFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\ncriteria\n=\nnew\nSampleSearchCriteria\n();\nvar\nfetchOptions\n=\nnew\nSampleFetchOptions\n();\n// get the first 100 results\nfetchOptions\n.\nfrom\n(\n0\n);\nfetchOptions\n.\ncount\n(\n100\n);\n// sort the results first by a type (ascending) and then by an identifier (descending)\nfetchOptions\n.\nsortBy\n().\ntype\n().\nasc\n();\nfetchOptions\n.\nsortBy\n().\nidentifier\n().\ndesc\n();\nv3\n.\nsearchSamples\n(\ncriteria\n,\nfetchOptions\n).\ndone\n(\nfunction\n(\nresult\n)\n{\n// because of pagination the list contains only the first 100 objects (or even less if there are fewer results found)\nconsole\n.\nlog\n(\nresult\n.\ngetObjects\n());\n// returns the number of all found results (i.e. potentially more than 100)\nconsole\n.\nlog\n(\nresult\n.\ngetTotalCount\n());\n});\n});\n</\nscript\n>", "heading": "Example with pagination and sorting", "level": 3, "chunk_index": 70, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_71", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Example with OR operator\n\n\nBy default all specified search criteria have to be fulfilled. If only\none criteria needs to be fulfilled use\ncriteria.withOrOperator()\nas in", "heading": "Example with OR operator", "level": 3, "chunk_index": 71, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_72", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "the following example:\n\nV3SearchWithOrOperatorExample.java\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.Sample\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.fetchoptions.SampleFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.search.SampleSearchCriteria\n;\npublic\nclass\nV3SearchWithOrOperatorExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// search for samples that are either in space with code MY_SPACE_CODE or of sample type with code MY_SAMPLE_TYPE_CODE\nSampleSearchCriteria\ncriteria\n=\nnew\nSampleSearchCriteria\n();\ncriteria\n.\nwithOrOperator\n();\ncriteria\n.\nwithSpace\n().\nwithCode\n().\nthatEquals\n(", "heading": "the following example:", "level": 3, "chunk_index": 72, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_73", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_TYPE_CODE\"\n\n);\n// tell the API to fetch the type for each returned sample\nSampleFetchOptions\nfetchOptions\n=\nnew\nSampleFetchOptions\n();\nfetchOptions\n.\nwithType\n();\nSearchResult\n<\nSample\n>\nresult\n=\nv3\n.\nsearchSamples\n(\nsessionToken\n,\ncriteria\n,\nfetchOptions\n);\nfor\n(\nSample\nsample", "heading": "\"MY_SAMPLE_TYPE_CODE\"", "level": 2, "chunk_index": 73, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_74", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nresult\n.\ngetObjects\n())\n{\nSystem\n.\nout\n.\nprintln\n(\n\"Sample \"\n+\nsample\n.\ngetIdentifier\n()\n+\n\" [\"\n+\nsample\n.\ngetType\n().\ngetCode\n()\n+\n\"]\"\n);\n}\n}\n}\nV3SearchWithOrOperatorExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/search/SampleSearchCriteria\"\n,\n\"as/dto/sample/fetchoptions/SampleFetchOptions\"\n],\nfunction\n(\nSampleSearchCriteria\n,\nSampleFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// search for samples that are in space with code MY_SPACE_CODE and are of sample type with code MY_SAMPLE_TYPE_CODE\nvar\ncriteria\n=\nnew\nSampleSearchCriteria\n();\ncriteria\n.\nwithOrOperator\n();\ncriteria\n.\nwithSpace\n().\nwithCode\n().\nthatEquals\n(", "heading": ":", "level": 3, "chunk_index": 74, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_75", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_TYPE_CODE\"\n\n);\n// tell the API to fetch type for each returned sample\nvar\nfetchOptions\n=\nnew\nSampleFetchOptions\n();\nfetchOptions\n.\nwithType\n();\nv3\n.\nsearchSamples\n(\ncriteria\n,\nfetchOptions\n).\ndone\n(\nfunction\n(\nresult\n)\n{\nresult\n.\ngetObjects\n().\nforEach\n(\nfunction\n(\nsample\n)\n{\nalert\n(\n\"Sample \"\n+\nsample\n.\ngetIdentifier\n()\n+\n\" [\"\n+\nsample\n.\ngetType\n().\ngetCode\n()\n+\n\"]\"\n);\n});\n});\n});\n</\nscript\n>\nExample with nested logical operators\n\nThe following code finds samples with perm ID that ends with “6” AND\n(with code that contains “-” OR that starts with “C”) AND (with\nexperiment OR of type whose code starts with “MASTER”).\nV3SearchWithNestedLogicalOperatorsExample.java\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.Sample\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.fetchoptions.SampleFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.search.SampleSearchCriteria\n;\npublic\nclass\nV3SearchWithRecursiveFetchOptionsExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleSearchCriteria\ncriteria\n=\nnew\nSampleSearchCriteria\n().\nwithAndOperator\n();\ncriteria\n.\nwithPermId\n().\nthatEndsWith\n(\n\"6\"\n);\nSampleSearchCriteria\nsubcriteria1\n=\ncriteria\n.\nwithSubcriteria\n().\nwithOrOperator\n();\nsubcriteria1\n.\nwithCode\n().\nthatContains\n(\n\"-\"\n);\nsubcriteria1\n.\nwithCode\n().\nthatStartsWith\n(\n\"C\"\n);\nSampleSearchCriteria\nsubcriteria2\n=\ncriteria\n.\nwithSubcriteria\n().\nwithOrOperator\n();\nsubcriteria2\n.\nwithExperiment\n();\nsubcriteria2\n.\nwithType\n().\nwithCode\n().\nthatStartsWith\n(", "heading": "\"MY_SAMPLE_TYPE_CODE\"", "level": 2, "chunk_index": 75, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_76", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MASTER\"\n\n);\n// tell the API to fetch all descendents for each returned sample\nSampleFetchOptions\nfetchOptions\n=\nnew\nSampleFetchOptions\n();\nSearchResult\n<\nSample\n>\nresult\n=\nv3\n.\nsearchSamples\n(\nsessionToken\n,\ncriteria\n,\nfetchOptions\n);\nfor\n(\nSample\nsample", "heading": "\"MASTER\"", "level": 2, "chunk_index": 76, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_77", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nresult\n.\ngetObjects\n())\n{\nSystem\n.\nout\n.\nprintln\n(\n\"Sample \"\n+\nsample\n.\ngetIdentifier\n()\n+\n\" [\"\n+\nsample\n.\ngetType\n().\ngetCode\n()\n+\n\"]\"\n);\n}\n}\n}\nV3SearchWithNestedLogicalOperatorsExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/search/SampleSearchCriteria\"\n,\n\"as/dto/sample/fetchoptions/SampleFetchOptions\"\n],\nfunction\n(\nSampleSearchCriteria\n,\nSampleFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\ncriteria\n=\nnew\nSampleSearchCriteria\n().\nwithAndOperator\n();\ncriteria\n.\nwithPermId\n().\nthatEndsWith\n(\n\"6\"\n);\nvar\nsubcriteria1\n=\ncriteria\n.\nwithSubcriteria\n().\nwithOrOperator\n();\nsubcriteria1\n.\nwithCode\n().\nthatContains\n(\n\"-\"\n);\nsubcriteria1\n.\nwithCode\n().\nthatStartsWith\n(\n\"C\"\n);\nvar\nsubcriteria2\n=\ncriteria\n.\nwithSubcriteria\n().\nwithOrOperator\n();\nsubcriteria2\n.\nwithExperiment\n();\nsubcriteria2\n.\nwithType\n().\nwithCode\n().\nthatStartsWith\n(", "heading": ":", "level": 3, "chunk_index": 77, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_78", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MASTER\"\n\n);\n// tell the API to fetch type for each returned sample\nvar\nfetchOptions\n=\nnew\nSampleFetchOptions\n();\nv3\n.\nsearchSamples\n(\ncriteria\n,\nfetchOptions\n).\ndone\n(\nfunction\n(\nresult\n)\n{\nresult\n.\ngetObjects\n().\nforEach\n(\nfunction\n(\nsample\n)\n{\nalert\n(\n\"Sample \"\n+\nsample\n.\ngetIdentifier\n()\n+\n\" [\"\n+\nsample\n.\ngetType\n().\ngetCode\n()\n+\n\"]\"\n);\n});\n});\n});\n</\nscript\n>\nExample with recursive fetch options\n\nIn order to get all descendent/acsendents of a sample fetch options can\nbe used recursively by\nusing\nfetchOptions.withChildrenUsing(fetchOptions)\nas in the following", "heading": "\"MASTER\"", "level": 2, "chunk_index": 78, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_79", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "example:\n\nV3SearchWithRecursiveFetchOptionsExample.java\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.Sample\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.fetchoptions.SampleFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.search.SampleSearchCriteria\n;\npublic\nclass\nV3SearchWithRecursiveFetchOptionsExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleSearchCriteria\ncriteria\n=\nnew\nSampleSearchCriteria\n();\ncriteria\n.\nwithType\n().\nwithCode\n().\nthatEquals\n(", "heading": "example:", "level": 3, "chunk_index": 79, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_80", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_TYPE_CODE\"\n\n);\n// tell the API to fetch all descendents for each returned sample\nSampleFetchOptions\nfetchOptions\n=\nnew\nSampleFetchOptions\n();\nfetchOptions\n.\nwithChildrenUsing\n(\nfetchOptions\n);\nSearchResult\n<\nSample\n>\nresult\n=\nv3\n.\nsearchSamples\n(\nsessionToken\n,\ncriteria\n,\nfetchOptions\n);\nfor\n(\nSample\nsample", "heading": "\"MY_SAMPLE_TYPE_CODE\"", "level": 2, "chunk_index": 80, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_81", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nresult\n.\ngetObjects\n())\n{\nSystem\n.\nout\n.\nprintln\n(\n\"Sample \"\n+\nrenderWithDescendants\n(\nsample\n));\n}\n}\nprivate\nstatic\nString\nrenderWithDescendants\n(\nSample\nsample\n)\n{\nStringBuilder\nbuilder\n=\nnew\nStringBuilder\n();\nfor\n(\nSample\nchild", "heading": ":", "level": 3, "chunk_index": 81, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_82", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nsample\n.\ngetChildren\n())\n{\nif\n(\nbuilder\n.\nlength\n()\n>\n0\n)\n{\nbuilder\n.\nappend\n(\n\", \"\n);\n}\nbuilder\n.\nappend\n(\nrenderWithDescendants\n(\nchild\n));\n}\nif\n(\nbuilder\n.\nlength\n()\n==\n0\n)\n{\nreturn\nsample\n.\ngetCode\n();\n}\nreturn\nsample\n.\ngetCode\n()\n+\n\" -> (\"\n+\nbuilder\n.\ntoString\n()\n+\n\")\"\n;\n}\n}\nV3SearchWithRecursiveFetchOptionsExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/search/SampleSearchCriteria\"\n,\n\"as/dto/sample/fetchoptions/SampleFetchOptions\"\n],\nfunction\n(\nSampleSearchCriteria\n,\nSampleFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\ncriteria\n=\nnew\nSampleSearchCriteria\n();\ncriteria\n.\nwithType\n().\nwithCode\n().\nthatEquals\n(", "heading": ":", "level": 3, "chunk_index": 82, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_83", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_TYPE_CODE\"\n\n);\n// tell the API to fetch all descendents for each returned sample\nvar\nfetchOptions\n=\nnew\nSampleFetchOptions\n();\nfetchOptions\n.\nwithChildrenUsing\n(\nfetchOptions\n);\nv3\n.\nsearchSamples\n(\ncriteria\n,\nfetchOptions\n).\ndone\n(\nfunction\n(\nresult\n)\n{\nresult\n.\ngetObjects\n().\nforEach\n(\nfunction\n(\nsample\n)\n{\nalert\n(\n\"Sample \"\n+\nrenderWithDescendants\n(\nsample\n));\n});\n});\nfunction\nrenderWithDescendants\n(\nsample\n)\n{\nvar\nchildren\n=\nsample\n.\ngetChildren\n();\nvar\nlist\n=\n\"\"\n;\nfor\n(\nvar\ni\n=\n0\n;\ni\n<\nchildren\n.\nlength\n;\ni\n++\n)\n{\nif\n(\nlist\n.\nlength\n>\n0\n)\n{\nlist\n+=\n\", \"\n;\n}\nlist\n+=\nrenderWithDescendants\n(\nchildren\n[\ni\n]);\n}\nif\n(\nchildren\n.\nlength\n==\n0\n)\n{\nreturn\nsample\n.\ngetCode\n();\n}\nreturn\nsample\n.\ngetCode\n()\n+\n\" -> (\"\n+\nlist\n+\n\")\"\n}\n});\n</\nscript\n>\nGlobal search\n", "heading": "\"MY_SAMPLE_TYPE_CODE\"", "level": 2, "chunk_index": 83, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_84", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "There are two kinds or global search:\n\nUsing thatContains() and thatContainsExactly() methods of\nGlobalSearchTextCriteria. This type of search performs the substring\nsearch in any field of any entity.", "heading": "There are two kinds or global search:", "level": 3, "chunk_index": 84, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_85", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Using thatMatches() method of GlobalSearchTextCriteria. This type of\n\nsearch performs lexical match using English dictionaly. If a\nmatching string is not a word it is matched as a whole (i.e. code\nwill match code only if a whole code string is provided).\nGlobal search searches for experiments, samples, data sets and materials\nby specifying a text snippet (or complete words) to be found in any type", "heading": "Using thatMatches() method of GlobalSearchTextCriteria. This type of", "level": 3, "chunk_index": 85, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_86", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "of meta data (entity attribute or property). Example:\n\nV3GlobalSearchExample.java\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.global.GlobalSearchObject\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.global.fetchoptions.GlobalSearchObjectFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.global.search.GlobalSearchCriteria\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.global.search.GlobalSearchObjectKind\n;\npublic\nclass\nV3GlobalSearchExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// search for any text matching 'default' but only among samples\nGlobalSearchCriteria\ncriteria\n=\nnew\nGlobalSearchCriteria\n();\ncriteria\n.\nwithObjectKind\n().\nthatIn\n(\nGlobalSearchObjectKind\n.", "heading": "of meta data (entity attribute or property). Example:", "level": 3, "chunk_index": 86, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_87", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SAMPLE\n\n);\ncriteria\n.\nwithText\n().\nthatMatches\n(\n\"default\"\n);\n// Fetch also the sample type\nGlobalSearchObjectFetchOptions\nfetchOptions\n=\nnew\nGlobalSearchObjectFetchOptions\n();\nfetchOptions\n.\nwithSample\n().\nwithType\n();\nSearchResult\n<\nGlobalSearchObject\n>\nresult\n=\nv3\n.\nsearchGlobally\n(\nsessionToken\n,\ncriteria\n,\nfetchOptions\n);\nfor\n(\nGlobalSearchObject\nobject", "heading": "SAMPLE", "level": 2, "chunk_index": 87, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_88", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nresult\n.\ngetObjects\n())\n{\nSystem\n.\nout\n.\nprintln\n(\nobject\n.\ngetObjectKind\n()\n+\n\": \"\n+\nobject\n.\ngetObjectIdentifier\n()\n+\n\" [\"\n+\nobject\n.\ngetSample\n().\ngetType\n().\ngetCode\n()\n+\n\"], score:\"\n+\nobject\n.\ngetScore\n()\n+\n\", match:\"\n+\nobject\n.\ngetMatch\n());\n}\n}\n}\nV3GlobalSearchExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/global/search/GlobalSearchCriteria\"\n,\n\"as/dto/global/search/GlobalSearchObjectKind\"\n,\n\"as/dto/global/fetchoptions/GlobalSearchObjectFetchOptions\"\n],\nfunction\n(\nGlobalSearchCriteria\n,\nGlobalSearchObjectKind\n,\nGlobalSearchObjectFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// search for any text matching 'default' but only among samples\nvar\ncriteria\n=\nnew\nGlobalSearchCriteria\n();\ncriteria\n.\nwithObjectKind\n().\nthatIn\n([\nGlobalSearchObjectKind\n.", "heading": ":", "level": 3, "chunk_index": 88, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_89", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SAMPLE\n\n]);\ncriteria\n.\nwithText\n().\nthatMatches\n(\n\"default\"\n);\n// Fetch also the sample type\nvar\nfetchOptions\n=\nnew\nGlobalSearchObjectFetchOptions\n();\nfetchOptions\n.\nwithSample\n().\nwithType\n();\nv3\n.\nsearchGlobally\n(\ncriteria\n,\nfetchOptions\n).\ndone\n(\nfunction\n(\nresult\n)\n{\nresult\n.\ngetObjects\n().\nforEach\n(\nfunction\n(\nobject\n)\n{\nalert\n(\nobject\n.\ngetObjectKind\n()\n+\n\": \"\n+\nobject\n.\ngetObjectIdentifier\n()\n+\n\" [\"\n+\nobject\n.\ngetSample\n().\ngetType\n().\ngetCode\n()\n+\n\"], score:\"\n+\nobject\n.\ngetScore\n()\n+\n\", match:\"\n+\nobject\n.\ngetMatch\n());\n});\n});\n});\n</\nscript\n>\nGetting entities\n\nThe methods for getting entities in V3 API are called: getSpaces,\ngetProjects, getExperiments, getSamples, getDataSets, getMaterials,\ngetVocabularyTerms, getTags. They all take a list of entity ids and\nfetch options as an input (please check “Searching entities” section for\nmore details on the fetch options). They return a map where the passed\nentity ids become the keys and values are the entities found for these\nids. If no entity was found for a given id or entity exists but you\ndon’t have access to it then there is no entry for such an id in the\nreturned map.\nExample\n\nV3GetExample.java\nimport\njava.util.Arrays\n;\nimport\njava.util.Map\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.Sample\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.fetchoptions.SampleFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.id.ISampleId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.id.SampleIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.id.SamplePermId\n;\npublic\nclass\nV3GetExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nISampleId\nid1\n=\nnew\nSampleIdentifier\n(", "heading": "SAMPLE", "level": 2, "chunk_index": 89, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_90", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE_2\"\n\n);\nISampleId\nid3\n=\nnew\nSamplePermId\n(\n\"20160115170726679-98669\"\n);\n// perm id of sample /MY_SPACE_CODE/MY_SAMPLE_CODE\nISampleId\nid4\n=\nnew\nSamplePermId\n(\n\"20160118115737079-98672\"\n);\n// perm id of sample /MY_SPACE_CODE/MY_SAMPLE_CODE_3\nISampleId\nid5\n=\nnew\nSamplePermId\n(", "heading": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE_2\"", "level": 2, "chunk_index": 90, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_91", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"I_DONT_EXIST\"\n\n);\nSampleFetchOptions\nfetchOptions\n=\nnew\nSampleFetchOptions\n();\nfetchOptions\n.\nwithProperties\n();\nMap\n<\nISampleId\n,\nSample\n>\nmap\n=\nv3\n.\ngetSamples\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nid1\n,\nid2\n,\nid3\n,\nid4\n,\nid5\n),\nfetchOptions\n);\nmap\n.\nget\n(\nid1\n);\n// returns sample /MY_SPACE_CODE/MY_SAMPLE_CODE\nmap\n.\nget\n(\nid2\n);\n// returns sample /MY_SPACE_CODE/MY_SAMPLE_CODE_2\nmap\n.\nget\n(\nid3\n);\n// returns sample /MY_SPACE_CODE/MY_SAMPLE_CODE\nmap\n.\nget\n(\nid4\n);\n// returns sample /MY_SPACE_CODE/MY_SAMPLE_CODE_3\nmap\n.\nget\n(\nid5\n);\n// returns null\n}\n}\nV3GetExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/id/SampleIdentifier\"\n,\n\"as/dto/sample/id/SamplePermId\"\n,\n\"as/dto/sample/fetchoptions/SampleFetchOptions\"\n],\nfunction\n(\n<PERSON><PERSON>Identifier\n,\nSamplePermId\n,\nSampleFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nid1\n=\nnew\nSampleIdentifier\n(", "heading": "\"I_DONT_EXIST\"", "level": 2, "chunk_index": 91, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_92", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE_2\"\n\n);\nvar\nid3\n=\nnew\nSamplePermId\n(\n\"20160115170726679-98669\"\n);\n// perm id of sample /MY_SPACE_CODE/MY_SAMPLE_CODE\nvar\nid4\n=\nnew\nSamplePermId\n(\n\"20160118115737079-98672\"\n);\n// perm id of sample   /MY_SPACE_CODE/MY_SAMPLE_CODE_3\nvar\nid5\n=\nnew\nSamplePermId\n(", "heading": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE_2\"", "level": 2, "chunk_index": 92, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_93", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"I_DONT_EXIST\"\n\n);\nvar\nfetchOptions\n=\nnew\nSampleFetchOptions\n();\nfetchOptions\n.\nwithProperties\n();\nv3\n.\ngetSamples\n([\nid1\n,\nid2\n,\nid3\n,\nid4\n,\nid5\n],\nfetchOptions\n).\ndone\n(\nfunction\n(\nmap\n)\n{\nmap\n[\nid1\n];\n// returns sample /MY_SPACE_CODE/MY_SAMPLE_CODE\nmap\n[\nid2\n];\n// returns sample /MY_SPACE_CODE/MY_SAMPLE_CODE_2\nmap\n[\nid3\n];\n// returns sample /MY_SPACE_CODE/MY_SAMPLE_CODE\nmap\n[\nid4\n];\n// returns sample /MY_SPACE_CODE/MY_SAMPLE_CODE_3\nmap\n[\nid5\n];\n// returns null\n});\n});\n</\nscript\n>\nDeleting entities\n\nThe methods for deleting entities in V3 API are called: deleteSpaces,\ndeleteProjects, deleteExperiments, deleteSamples, deleteDataSets,\ndeleteMaterials, deleteVocabularyTerms, deleteTags. The delete methods\nfor spaces, projects, materials, vocabulary terms, tags perform a\npermanent deletion (there is no trash can for these entities - deletion\ncannot be reverted). The delete methods for experiments, samples and\ndata sets perform a logical deletion (move entities to the trash can)\nand return a deletion id. This deletion id can be used for either\nconfirming the logical deletion to remove the entities permanently or\nreverting the logical deletion to take the entities out from the trash\ncan.\nExample\n\nV3DeleteExample.java\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.deletion.id.IDeletionId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.delete.SampleDeletionOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.id.ISampleId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.id.SampleIdentifier\n;\npublic\nclass\nV3DeleteExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nISampleId\nid1\n=\nnew\nSampleIdentifier\n(", "heading": "\"I_DONT_EXIST\"", "level": 2, "chunk_index": 93, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_94", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE_2\"\n\n);\nSampleDeletionOptions\ndeletionOptions\n=\nnew\nSampleDeletionOptions\n();\ndeletionOptions\n.\nsetReason\n(\n\"Testing logical deletion\"\n);\n// logical deletion (move objects to the trash can)\nIDeletionId\ndeletionId\n=\nv3\n.\ndeleteSamples\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nid1\n,\nid2\n),\ndeletionOptions\n);\n// you can use the deletion id to confirm the deletion (permanently delete objects)\nv3\n.\nconfirmDeletions\n(\nsessionToken\n,\nArrays\n.\nasList\n(\ndeletionId\n));\n// you can use the deletion id to revert the deletion (get the objects out from the trash can)\nv3\n.\nrevertDeletions\n(\nsessionToken\n,\nArrays\n.\nasList\n(\ndeletionId\n));\n}\n}\nV3DeleteExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/id/SampleIdentifier\"\n,\n\"as/dto/sample/delete/SampleDeletionOptions\"\n],\nfunction\n(\nSampleIdentifier\n,\nSampleDeletionOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nid1\n=\nnew\nSampleIdentifier\n(", "heading": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE_2\"", "level": 2, "chunk_index": 94, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_95", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE_2\"\n\n);\nvar\ndeletionOptions\n=\nnew\nSampleDeletionOptions\n();\ndeletionOptions\n.\nsetReason\n(\n\"Testing logical deletion\"\n);\n// logical deletion (move objects to the trash can)\nv3\n.\ndeleteSamples\n([\nid1\n,\nid2\n],\ndeletionOptions\n).\ndone\n(\nfunction\n(\ndeletionId\n)\n{\n// you can use the deletion id to confirm the deletion (permanently delete objects)\nv3\n.\nconfirmDeletions\n([\ndeletionId\n]);\n// you can use the deletion id to revert the deletion (get the objects out from the trash can)\nv3\n.\nrevertDeletions\n([\ndeletionId\n]);\n});\n});\n</\nscript\n>\nSearching entity types\n\nThe following search methods allows to search for entity types including\nall assigned property", "heading": "\"/MY_SPACE_CODE/MY_SAMPLE_CODE_2\"", "level": 2, "chunk_index": 95, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_96", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "types:\n\nsearchDataSetTypes\n,\nsearchExperimentTypes\n,\nsearchMaterialTypes\nand\nsearchSampleTypes\n. Here is an example which will search for all", "heading": "types:", "level": 3, "chunk_index": 96, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_97", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "sample types and assigned property types:\n\nV3SearchTypesExample.java\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.property.PropertyAssignment\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.SampleType\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.fetchoptions.SampleTypeFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.search.SampleTypeSearchCriteria\n;\npublic\nclass\nV3SearchTypesExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleTypeSearchCriteria\nsearchCriteria\n=\nnew\nSampleTypeSearchCriteria\n();\nSampleTypeFetchOptions\nfetchOptions\n=\nnew\nSampleTypeFetchOptions\n();\nfetchOptions\n.\nwithPropertyAssignments\n().\nwithPropertyType\n();\nSearchResult\n<\nSampleType\n>\nresult\n=\nv3\n.\nsearchSampleTypes\n(\nsessionToken\n,\nsearchCriteria\n,\nfetchOptions\n);\nfor\n(\nSampleType\nsampleType", "heading": "sample types and assigned property types:", "level": 3, "chunk_index": 97, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_98", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nresult\n.\ngetObjects\n())\n{\nSystem\n.\nout\n.\nprintln\n(\nsampleType\n.\ngetCode\n());\nfor\n(\nPropertyAssignment\nassignment", "heading": ":", "level": 3, "chunk_index": 98, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_99", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nsampleType\n.\ngetPropertyAssignments\n())\n{\nSystem\n.\nout\n.\nprintln\n(\n\"  \"\n+\nassignment\n.\ngetPropertyType\n().\ngetCode\n()\n+\n(\nassignment\n.\nisMandatory\n()\n?\n\"*\"", "heading": ":", "level": 3, "chunk_index": 99, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_100", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\n\"\"\n));\n}\n}\n}\n}\nV3SearchTypesExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/sample/search/SampleTypeSearchCriteria\"\n,\n\"as/dto/sample/fetchoptions/SampleTypeFetchOptions\"\n],\nfunction\n(\nSampleTypeSearchCriteria\n,\nSampleTypeFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// here we are interested only in the last updates of samples and projects\nvar\ncriteria\n=\nnew\nSampleTypeSearchCriteria\n();\nvar\nfetchOptions\n=\nnew\nSampleTypeFetchOptions\n();\nfetchOptions\n.\nwithPropertyAssignments\n().\nwithPropertyType\n();\nv3\n.\nsearchSampleTypes\n(\ncriteria\n,\nfetchOptions\n).\ndone\n(\nfunction\n(\nresult\n)\n{\nresult\n.\ngetObjects\n().\nforEach\n(\nfunction\n(\nsampleType\n)\n{\nvar\nmsg\n=\nsampleType\n.\ngetCode\n();\nvar\nassignments\n=\nsampleType\n.\ngetPropertyAssignments\n();\nfor\n(\nvar\ni\n=\n0\n;\ni\n<\nassignments\n.\nlength\n;\ni\n++\n)\n{\nmsg\n+=\n\"\\n  \"\n+\nassignments\n[\ni\n].\ngetPropertyType\n().\ngetCode\n();\n}\nalert\n(\nmsg\n);\n});\n});\n});\n</\nscript\n>\nModifications\n\nThe API allows to ask for the latest modification (UPDATE or\nCREATE_OR_DELETE) for groups of objects of various kinds (see\nclass\nch.ethz.sis.openbis.generic.asapi.v3.dto.objectkindmodification.ObjectKind\nfor\na complete list). This feature of the openBIS API helps GUI clients to\nupdate views automatically. Here is an example which asks for the latest", "heading": ":", "level": 3, "chunk_index": 100, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_101", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "project and sample update:\n\nV3SearchObjectKindModificationsExample.java\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.objectkindmodification.ObjectKind\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.objectkindmodification.ObjectKindModification\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.objectkindmodification.OperationKind\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.objectkindmodification.fetchoptions.ObjectKindModificationFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.objectkindmodification.search.ObjectKindModificationSearchCriteria\n;\npublic\nclass\nV3SearchObjectKindModificationsExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// here we are interested only in the last updates of samples and projects\nObjectKindModificationSearchCriteria\ncriteria\n=\nnew\nObjectKindModificationSearchCriteria\n();\ncriteria\n.\nwithObjectKind\n().\nthatIn\n(\nObjectKind\n.", "heading": "project and sample update:", "level": 3, "chunk_index": 101, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_102", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "UPDATE\n\n);\nObjectKindModificationFetchOptions\nfetchOptions\n=\nnew\nObjectKindModificationFetchOptions\n();\nSearchResult\n<\nObjectKindModification\n>\nresult\n=\nv3\n.\nsearchObjectKindModifications\n(\nsessionToken\n,\ncriteria\n,\nfetchOptions\n);\nfor\n(\nObjectKindModification\nmodification", "heading": "UPDATE", "level": 2, "chunk_index": 102, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_103", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nresult\n.\ngetObjects\n())\n{\nSystem\n.\nout\n.\nprintln\n(\n\"The last \"\n+\nmodification\n.\ngetOperationKind\n()\n+\n\" of an entity of kind \"\n+\nmodification\n.\ngetObjectKind\n()\n+\n\" occured at \"\n+\nmodification\n.\ngetLastModificationTimeStamp\n());\n}\n}\n}\nV3SearchObjectKindModificationsExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/objectkindmodification/search/ObjectKindModificationSearchCriteria\"\n,\n\"as/dto/objectkindmodification/ObjectKind\"\n,\n\"as/dto/objectkindmodification/OperationKind\"\n,\n\"as/dto/objectkindmodification/fetchoptions/ObjectKindModificationFetchOptions\"\n],\nfunction\n(\nObjectKindModificationSearchCriteria\n,\nObjectKind\n,\nOperationKind\n,\nObjectKindModificationFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\n// here we are interested only in the last updates of samples and projects\nvar\ncriteria\n=\nnew\nObjectKindModificationSearchCriteria\n();\ncriteria\n.\nwithObjectKind\n().\nthatIn\n([\nObjectKind\n.", "heading": ":", "level": 3, "chunk_index": 103, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_104", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "UPDATE\n\n]);\nvar\nfetchOptions\n=\nnew\nObjectKindModificationFetchOptions\n();\nv3\n.\nsearchObjectKindModifications\n(\ncriteria\n,\nfetchOptions\n).\ndone\n(\nfunction\n(\nresult\n)\n{\nresult\n.\ngetObjects\n().\nforEach\n(\nfunction\n(\nmodification\n)\n{\nalert\n(\n\"The last \"\n+\nmodification\n.\ngetOperationKind\n()\n+\n\" of an entity of kind \"\n+\nmodification\n.\ngetObjectKind\n()\n+\n\" occured at \"\n+\nmodification\n.\ngetLastModificationTimeStamp\n());\n});\n});\n});\n</\nscript\n>", "heading": "UPDATE", "level": 2, "chunk_index": 104, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_105", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Custom AS Services\n\n\nIn order to extend openBIS API new custom services can be established by core plugins of type\nservices\n(see", "heading": "Custom AS Services", "level": 3, "chunk_index": 105, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_106", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Custom Application Server Services\n\n). The API offers a method to search for a service and to execute a service.\nSearch for custom services\n\nAs with any other search method\nsearchCustomASServices()\nneeds a search criteria\nCustomASServiceSearchCriteria\nand fetch options\nCustomASServiceFetchOptions\n. The following example returns all available custom AS services.\nExample\n\nV3SearchCustomASServicesExample.java\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.service.CustomASService\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.service.fetchoptions.CustomASServiceFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.service.search.CustomASServiceSearchCriteria\n;\npublic\nclass\nV3SearchCustomASServicesExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nCustomASServiceSearchCriteria\ncriteria\n=\nnew\nCustomASServiceSearchCriteria\n();\nCustomASServiceFetchOptions\nfetchOptions\n=\nnew\nCustomASServiceFetchOptions\n();\nSearchResult\n<\nCustomASService\n>\nresult\n=\nv3\n.\nsearchCustomASServices\n(\nsessionToken\n,\ncriteria\n,\nfetchOptions\n);\nfor\n(\nCustomASService\nservice", "heading": "Custom Application Server Services", "level": 3, "chunk_index": 106, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_107", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nresult\n.\ngetObjects\n())\n{\nSystem\n.\nout\n.\nprintln\n(\nservice\n.\ngetCode\n()\n+\n\": \"\n+\nservice\n.\ngetLabel\n()\n+\n\" (\"\n+\nservice\n.\ngetDescription\n()\n+\n\")\"\n);\n}\n}\n}\nV3SearchCustomASServicesExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/service/search/CustomASServiceSearchCriteria\"\n,\n\"as/dto/service/fetchoptions/CustomASServiceFetchOptions\"\n],\nfunction\n(\nCustomASServiceSearchCriteria\n,\nCustomASServiceFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\ncriteria\n=\nnew\nCustomASServiceSearchCriteria\n();\nvar\nfetchOptions\n=\nnew\nCustomASServiceFetchOptions\n();\nv3\n.\nsearchCustomASServices\n(\ncriteria\n,\nfetchOptions\n).\ndone\n(\nfunction\n(\nresult\n)\n{\nresult\n.\ngetObjects\n().\nforEach\n(\nfunction\n(\nservice\n)\n{\nalert\n(\nservice\n.\ngetCode\n()\n+\n\": \"\n+\nservice\n.\ngetLabel\n()\n+\n\" (\"\n+\nservice\n.\ngetDescription\n()\n+\n\")\"\n);\n});\n});\n});\n</\nscript\n>\nExecute a custom service\n\nIn order to execute a custom AS service its code is needed. In addition\na set of key-value pairs can be provided. The key has to be a string\nwhereas the value can be any object. Note, that in case of Java the\nobject has be an instance of class which Java serializable. The\nkey-value pairs are added to\nCustomASServiceExecutionOptions\nobject by\ninvoking\nwithParameter()\nfor each pair.\nThe result can be any object (again it has to be Java serializable in\nthe Java case). In a Java client the result will usually be casted for\nfurther processing.\nExample\n\nV3ExecuteCustomASServiceExample.java\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.service.CustomASServiceExecutionOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.service.id.CustomASServiceCode\n;\npublic\nclass\nV3ExecuteCustomASServiceExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nCustomASServiceCode\nid\n=\nnew\nCustomASServiceCode\n(\n\"example-service\"\n);\nCustomASServiceExecutionOptions\noptions\n=\nnew\nCustomASServiceExecutionOptions\n().\nwithParameter\n(\n\"space-code\"\n,", "heading": ":", "level": 3, "chunk_index": 107, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_108", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"TEST\"\n\n);\nObject\nresult\n=\nv3\n.\nexecuteCustomASService\n(\nsessionToken\n,\nid\n,\noptions\n);\nSystem\n.\nout\n.\nprintln\n(\n\"Result: \"\n+\nresult\n);\n}\n}\nV3ExecuteCustomASServiceExample.html\n<\nscript\n>\nrequire\n([\n\"as/dto/service/id/CustomASServiceCode\"\n,\n\"as/dto/service/CustomASServiceExecutionOptions\"\n],\nfunction\n(\nCustomASServiceCode\n,\nCustomASServiceExecutionOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nid\n=\nnew\nCustomASServiceCode\n(\n\"example-service\"\n);\nvar\noptions\n=\nnew\nCustomASServiceExecutionOptions\n().\nwithParameter\n(\n\"space-code\"\n,", "heading": "\"TEST\"", "level": 2, "chunk_index": 108, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.996026"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_109", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"TEST\"\n\n);\nv3\n.\nexecuteCustomASService\n(\nid\n,\noptions\n).\ndone\n(\nfunction\n(\nresult\n)\n{\nalert\n(\nresult\n);\n});\n});\n</\nscript\n>\nArchiving / unarchiving data sets\n\nThe API provides the following methods for handling the data set\narchiving: archiveDataSets and unarchiveDataSets. Both methods schedule\nthe operation to be executed asynchronously, i.e. once\narchiveDataSets/unarchiveDataSets method call finishes the requested\ndata sets are only scheduled for the archiving/unarchiving but are not\nin the archive/store yet.\nArchiving data sets\n\nExample\n\nV3ArchiveDataSetsExample.java\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.archive.DataSetArchiveOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.id.DataSetPermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.id.IDataSetId\n;\nimport\nch.systemsx.cisd.common.spring.HttpInvokerUtils\n;\npublic\nclass\nV3ArchiveDataSetsExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nIDataSetId\nid1\n=\nnew\nDataSetPermId\n(\n\"20160524154020607-2266\"\n);\nIDataSetId\nid2\n=\nnew\nDataSetPermId\n(\n\"20160524154020607-2267\"\n);\nDataSetArchiveOptions\noptions\n=\nnew\nDataSetArchiveOptions\n();\n// With removeFromDataStore flag set to true data sets are moved to the archive.\n// With removeFromDataStore flag set to false data sets are copied to the archive.\n// Default value is true (move to the archive).\noptions\n.\nsetRemoveFromDataStore\n(\nfalse\n);\n// Schedules archiving of the specified data sets. Archiving itself is executed asynchronously.\nv3\n.\narchiveDataSets\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nid1\n,\nid2\n),\noptions\n);\nSystem\n.\nout\n.\nprintln\n(\n\"Archiving scheduled\"\n);\n}\n}\nV3ArchiveDataSetsExample.html\n<\nscript\n>\nrequire\n([\n\"openbis\"\n,\n\"as/dto/dataset/id/DataSetPermId\"\n,\n\"as/dto/dataset/archive/DataSetArchiveOptions\"\n],\nfunction\n(\nopenbis\n,\nDataSetPermId\n,\nDataSetArchiveOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nid1\n=\nnew\nDataSetPermId\n(\n\"20160524154020607-2266\"\n)\nvar\nid2\n=\nnew\nDataSetPermId\n(\n\"20160524154020607-2267\"\n)\nvar\noptions\n=\nnew\nDataSetArchiveOptions\n();\n// With removeFromDataStore flag set to true data sets are moved to the archive.\n// With removeFromDataStore flag set to false data sets are copied to the archive.\n// Default value is true (move to the archive).\noptions\n.\nsetRemoveFromDataStore\n(\nfalse\n);\n// Schedules archiving of the specified data sets. Archiving itself is executed asynchronously.\nv3\n.\narchiveDataSets\n([\nid1\n,\nid2\n],\noptions\n).\ndone\n(\nfunction\n()\n{\nalert\n(\n\"Archiving scheduled\"\n);\n});\n});\n});\n</\nscript\n>\nUnarchiving data sets\n\nExample\n\nV3UnarchiveDataSetsExample.java\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.id.DataSetPermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.id.IDataSetId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.unarchive.DataSetUnarchiveOptions\n;\nimport\nch.systemsx.cisd.common.spring.HttpInvokerUtils\n;\npublic\nclass\nV3UnarchiveDataSetsExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nIDataSetId\nid1\n=\nnew\nDataSetPermId\n(\n\"20160524154020607-2266\"\n);\nIDataSetId\nid2\n=\nnew\nDataSetPermId\n(\n\"20160524154020607-2267\"\n);\nDataSetUnarchiveOptions\noptions\n=\nnew\nDataSetUnarchiveOptions\n();\n// Schedules unarchiving of the specified data sets. Unarchiving itself is executed asynchronously.\nv3\n.\nunarchiveDataSets\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nid1\n,\nid2\n),\noptions\n);\nSystem\n.\nout\n.\nprintln\n(\n\"Unarchiving scheduled\"\n);\n}\n}\nV3UnarchiveDataSetsExample.html\n<\nscript\n>\nrequire\n([\n\"openbis\"\n,\n\"as/dto/dataset/id/DataSetPermId\"\n,\n\"as/dto/dataset/unarchive/DataSetUnarchiveOptions\"\n],\nfunction\n(\nopenbis\n,\nDataSetPermId\n,\nDataSetUnarchiveOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nid1\n=\nnew\nDataSetPermId\n(\n\"20160524154020607-2266\"\n)\nvar\nid2\n=\nnew\nDataSetPermId\n(\n\"20160524154020607-2267\"\n)\nvar\noptions\n=\nnew\nDataSetUnarchiveOptions\n();\n// Schedules unarchiving of the specified data sets. Unarchiving itself is executed asynchronously.\nv3\n.\nunarchiveDataSets\n([\nid1\n,\nid2\n],\noptions\n).\ndone\n(\nfunction\n()\n{\nalert\n(\n\"Unarchiving scheduled\"\n);\n});\n});\n});\n</\nscript\n>", "heading": "\"TEST\"", "level": 2, "chunk_index": 109, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_110", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Executing Operations\n\n\nThe V3 API provides you with methods that allow you to create, update,\nget, search and delete entities, archive and unarchive datasets, execute\ncustom services and much more. With these methods you can\nprogrammatically access most of the openBIS features to build your own\nwebapps, dropboxes or services. Even though these methods are quite\ndifferent, there are some things that they all have in common:\neach method is executed in its own separate transaction\neach method is executed synchronously\nLet’s think about what it really means. Separate transactions make two\n(even subsequent) method calls completely unrelated. For instance, when\nyou make a call to create experiments and then another call to create\nsamples, then even if the sample creation fails the experiments, that\nhad been already created, would remain in the system. Most of the time\nthis is exactly what we want but not always. There are times when we\nwould like to create either both experiments and samples or nothing if\nsomething is wrong. A good example would be an import of some file that\ncontains both experiments and samples. We would like to be able to\nimport the file, fail if it is wrong, correct the file and import it\nagain. With separate transactions we would end up with some things\nalready created after the first failed import and we wouldn’t be able to\nreimport the corrected file again as some things would be already in the\nsystem.\nSynchronous method execution is also something what we expect most of\nthe time. You call a method and it returns once all the work is done.\nFor instance, when we call a method to create samples we know that once\nthe method finishes all the samples have been created in the\nsystem. This makes perfect sense when we need to execute operations that\ndepend on each other, e.g. we can create data sets and attach them to\nsamples only after the samples had been created. Just as with the\nseparate transactions, there are cases when synchronous method execution\nis limiting. Let’s use the file import example again. What would happen\nif a file we wanted to import contained hundreds of thousands of\nentities? The import would probably take a very long time. Our\nsynchronous method call would not return until all the entities have\nbeen created which means we would also block a script/program that makes\nthis method call for a very long time. We could of course create a\nseparate thread in our script/program to overcome this problem but that\nwould add up more complexity. It would be also nice to notify a user\nonce such an operation finishes or fails, e.g. by sending an email.\nUnfortunately that would mean we have to keep our script/program running\nuntil the operation finishes or fails to send such an email. What about\na progress information for running executions or a history of previous\noperations and their results? That would be nice but it would increase\nthe complexity of our script/program even more.", "heading": "Executing Operations", "level": 3, "chunk_index": 110, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_111", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Therefore, if you want to:\n\nexecute multiple operations in a single transaction\nexecute operations asynchronously\nmonitor progress of operations\nreceive notifications about finished/failed operations\nkeep history of operations and their results", "heading": "Therefore, if you want to:", "level": 3, "chunk_index": 111, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_112", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "you should use:\n\nexecuteOperations method to execute your operations\ngetOperationExecutions and searchOperationExecutions methods to\nretrieve information about operation executions (e.g. progress,\nresults or errors)\nupdateOperationExecutions and deleteOperationExecutions methods to\ncontrol what information should be still kept for a given operation\nexecution and what information can be already removed\nMore details on each of these methods in presented in the sections\nbelow. Please note that all of the described methods are available in\nboth Javascript and Java.\nMethod executeOperations\n\nThis method can be used to execute one or many operations either\nsynchronously or asynchronously. Operations are always executed in a\nsingle transaction (a failure of a single operation triggers a rollback\nof all the operations). The executeOperations method can be used to\nexecute any of the IApplicationServerApi methods (except for\nlogin/logout and executeOperations itself), i.e. for each\nIApplicationServerApi method there is a corresponding operation class\n(class that implements IOperation interface). For instance,\nIApplicationServerApi.createSpaces method is represented by\nCreateSpacesOperation class, IApplicationServerApi.updateSpaces method\nby UpdateSpacesOperation class etc.\nAsynchronous operation execution\n\nAn asynchronous executeOperations invocation only schedules operations\nfor the execution and then immediately returns. Results of the scheduled\noperations can be retrieved later with getOperationExecutions or\nsearchOperationExecutions methods.\nBecause the operations are scheduled to be executed later (in a separate\nthread) a regular try/catch block around executeOperations method will\nonly catch exceptions related with scheduling the operations for the\nexecution, but NOT the exceptions thrown by the operations during the\nexecution. To check for errors that occurred during the execution please\nuse getOperationExecutions and searchOperationExecutions methods once\nthe execution finishes.\nIn order to execute operations asynchronously, executeOperations has to\nbe used with AsynchronousOperationExecutionOptions. With such options,\nthe method returns AsynchronousOperationExecutionResults object.\nAsynchronousOperationExecutionResults object contains automatically\ngenerated executionId that can be used for retrieving additional\ninformation about the execution, fetching the results or errors.\nDuring its life an asynchronous execution goes through the following", "heading": "you should use:", "level": 3, "chunk_index": 112, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_113", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "states:\n\nNEW - execution has been just created with executeOperations method\nSCHEDULED - execution has been added to a thread pool queue and is\nwaiting for a free thread\nRUNNING - execution has been picked from a thread pool queue by a\nfree thread and is currently executing\nFINISHED/FAILED - if execution finishes successfully then execution\nstate is changed to FINISHED, if anything goes wrong it is changed\nto FAILED\nV3ExecuteOperationsAsynchronous.java\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.entitytype.id.EntityTypePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.experiment.id.ExperimentIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.AsynchronousOperationExecutionOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.AsynchronousOperationExecutionResults\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.CreateSamplesOperation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.SampleCreation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.id.SpacePermId\n;\npublic\nclass\nV3ExecuteOperationsAsynchronous\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleCreation\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "states:", "level": 3, "chunk_index": 113, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_114", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\nCreateSamplesOperation\noperation\n=\nnew\nCreateSamplesOperation\n(\nsample\n);\nAsynchronousOperationExecutionResults\nresults\n=\n(\nAsynchronousOperationExecutionResults\n)\nv3\n.\nexecuteOperations\n(\nsessionToken\n,\nArrays\n.\nasList\n(\noperation\n),\nnew\nAsynchronousOperationExecutionOptions\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Execution id: \"\n+\nresults\n.\ngetExecutionId\n());\n}\n}\nV3ExecuteOperationsAsynchronous.html\n<\nscript\n>\nrequire\n([\n\"openbis\"\n,\n\"as/dto/sample/create/SampleCreation\"\n,\n\"as/dto/entitytype/id/EntityTypePermId\"\n,\n\"as/dto/space/id/SpacePermId\"\n,\n\"as/dto/experiment/id/ExperimentIdentifier\"\n,\n\"as/dto/sample/create/CreateSamplesOperation\"\n,\n\"as/dto/operation/AsynchronousOperationExecutionOptions\"\n],\nfunction\n(\nopenbis\n,\nSampleCreation\n,\nEntityTypePermId\n,\nSpacePermId\n,\nExperimentIdentifier\n,\nCreateSamplesOperation\n,\nAsynchronousOperationExecutionOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 114, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_115", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\nvar\noperation\n=\nnew\nCreateSamplesOperation\n([\nsample\n]);\nv3\n.\nexecuteOperations\n([\noperation\n],\nnew\nAsynchronousOperationExecutionOptions\n()).\ndone\n(\nfunction\n(\nresults\n)\n{\nconsole\n.\nlog\n(\n\"Execution id: \"\n+\nresults\n.\ngetExecutionId\n());\n});\n});\n</\nscript\n>\nSynchronous operation execution\n\nA synchronous executeOperations invocation immediately executes all the\noperations. Any exceptions thrown by the executed operations can be\ncaught with a regular try/catch block around executeOperations method.\nIn order to execute operations synchronously, executeOperations has to\nbe used with SynchronousOperationExecutionOptions. With such options,\nthe method returns SynchronousOperationExecutionResults object.\nSynchronousOperationExecutionResults object contains the results for all\nthe executed operations.\nIn contrast to the asynchronous version, the synchronous call requires\nexecutionId to be explicitly set in SynchronousOperationExecutionOptions\nfor the additional information to be gathered about the execution.\nDuring its life a synchronous execution goes through the following", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 115, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_116", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "states:\n\nNEW - execution has been just created with executeOperations method\nRUNNING - execution is being executed by the same thread as\nexecuteOperations method\nFINISHED/FAILED - if execution finishes successfully then execution\nstate is changed to FINISHED, if anything goes wrong it is changed\nto FAILED\nV3ExecuteOperationsSynchronous.java\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.entitytype.id.EntityTypePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.experiment.id.ExperimentIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.SynchronousOperationExecutionOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.SynchronousOperationExecutionResults\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.CreateSamplesOperation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.CreateSamplesOperationResult\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.SampleCreation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.id.SpacePermId\n;\npublic\nclass\nV3ExecuteOperationsSynchronous\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleCreation\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "states:", "level": 3, "chunk_index": 116, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_117", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\nCreateSamplesOperation\noperation\n=\nnew\nCreateSamplesOperation\n(\nsample\n);\nSynchronousOperationExecutionResults\nresults\n=\n(\nSynchronousOperationExecutionResults\n)\nv3\n.\nexecuteOperations\n(\nsessionToken\n,\nArrays\n.\nasList\n(\noperation\n),\nnew\nSynchronousOperationExecutionOptions\n());\nCreateSamplesOperationResult\nresult\n=\n(\nCreateSamplesOperationResult\n)\nresults\n.\ngetResults\n().\nget\n(\n0\n);\nSystem\n.\nout\n.\nprintln\n(\n\"Sample id: \"\n+\nresult\n.\ngetObjectIds\n());\n}\n}\nV3ExecuteOperationsSynchronous.html\n<\nscript\n>\nrequire\n([\n\"openbis\"\n,\n\"as/dto/sample/create/SampleCreation\"\n,\n\"as/dto/entitytype/id/EntityTypePermId\"\n,\n\"as/dto/space/id/SpacePermId\"\n,\n\"as/dto/experiment/id/ExperimentIdentifier\"\n,\n\"as/dto/sample/create/CreateSamplesOperation\"\n,\n\"as/dto/operation/SynchronousOperationExecutionOptions\"\n],\nfunction\n(\nopenbis\n,\nSampleCreation\n,\nEntityTypePermId\n,\nSpacePermId\n,\nExperimentIdentifier\n,\nCreateSamplesOperation\n,\nSynchronousOperationExecutionOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 117, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_118", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\nvar\noperation\n=\nnew\nCreateSamplesOperation\n([\nsample\n]);\nv3\n.\nexecuteOperations\n([\noperation\n],\nnew\nSynchronousOperationExecutionOptions\n()).\ndone\n(\nfunction\n(\nresults\n)\n{\nvar\nresult\n=\nresults\n.\ngetResults\n()[\n0\n];\nconsole\n.\nlog\n(\n\"Sample id: \"\n+\nresult\n.\ngetObjectIds\n());\n});\n});\n</\nscript\n>\nNotifications\n\nThe executeOperations method can notify about finished or failed\noperation executions. At the moment the only supported notification\nmethod is email (OperationExecutionEmailNotification).", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 118, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_119", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "For successfully finished executions an email contains:\n\nexecution id\nexecution description\nlist of operation summaries and operation results", "heading": "For successfully finished executions an email contains:", "level": 3, "chunk_index": 119, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_120", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "For failed executions an email contains:\n\nexecution id\nexecution description\nlist of operation summaries\nerror\nV3ExecuteOperationsEmailNotification.java\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.entitytype.id.EntityTypePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.experiment.id.ExperimentIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.AsynchronousOperationExecutionOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.AsynchronousOperationExecutionResults\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.OperationExecutionEmailNotification\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.CreateSamplesOperation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.SampleCreation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.id.SpacePermId\n;\npublic\nclass\nV3ExecuteOperationsEmailNotification\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleCreation\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "For failed executions an email contains:", "level": 3, "chunk_index": 120, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_121", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\nCreateSamplesOperation\noperation\n=\nnew\nCreateSamplesOperation\n(\nsample\n);\nAsynchronousOperationExecutionOptions\noptions\n=\nnew\nAsynchronousOperationExecutionOptions\n();\noptions\n.\nsetNotification\n(\nnew\nOperationExecutionEmailNotification\n(\n\"<EMAIL>\"\n,\n\"<EMAIL>\"\n));\nAsynchronousOperationExecutionResults\nresults\n=\n(\nAsynchronousOperationExecutionResults\n)\nv3\n.\nexecuteOperations\n(\nsessionToken\n,\nArrays\n.\nasList\n(\noperation\n),\noptions\n);\nSystem\n.\nout\n.\nprintln\n(\n\"Execution id: \"\n+\nresults\n.\ngetExecutionId\n());\n}\n}\nV3ExecuteOperationsEmailNotification.html\n<\nscript\n>\nrequire\n([\n\"openbis\"\n,\n\"as/dto/sample/create/SampleCreation\"\n,\n\"as/dto/entitytype/id/EntityTypePermId\"\n,\n\"as/dto/space/id/SpacePermId\"\n,\n\"as/dto/experiment/id/ExperimentIdentifier\"\n,\n\"as/dto/sample/create/CreateSamplesOperation\"\n,\n\"as/dto/operation/AsynchronousOperationExecutionOptions\"\n,\n\"as/dto/operation/OperationExecutionEmailNotification\"\n],\nfunction\n(\nopenbis\n,\nSampleCreation\n,\nEntityTypePermId\n,\nSpacePermId\n,\nExperimentIdentifier\n,\nCreateSamplesOperation\n,\nAsynchronousOperationExecutionOptions\n,\nOperationExecutionEmailNotification\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 121, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_122", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\nvar\noperation\n=\nnew\nCreateSamplesOperation\n([\nsample\n]);\nvar\noptions\n=\nnew\nAsynchronousOperationExecutionOptions\n();\noptions\n.\nsetNotification\n(\nnew\nOperationExecutionEmailNotification\n([\n\"<EMAIL>\"\n,\n\"<EMAIL>\"\n]));\nv3\n.\nexecuteOperations\n([\noperation\n],\noptions\n).\ndone\n(\nfunction\n(\nresults\n)\n{\nconsole\n.\nlog\n(\n\"Execution id: \"\n+\nresults\n.\ngetExecutionId\n());\n});\n});\n</\nscript\n>\nMethod getOperationExecutions / searchOperationExecutions\n\nOperation execution information can be fetched by an owner of an\nexecution (i.e. a person that called executeOperations method) or an\nadmin. Both getOperationExecutions and searchOperationExecutions methods\nwork similar to the other get/search methods in the V3 API.\nThe operation execution information that both methods return can be", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 122, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_123", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "divided into 3 categories:\n\nbasic information (code, state, owner, description, creationDate,\nstartDate, finishDate etc.)\nsummary information (summary of operations, progress, error,\nresults)\ndetailed information (details of operations, progress, error,\nresults)\nEach category can have a different availability time (i.e. time for how\nlong a given information is stored in the system). The availability\ntimes can be set via the executeOperations method options (both", "heading": "divided into 3 categories:", "level": 3, "chunk_index": 123, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_124", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "AsynchronousOperationExecutionOptions):\n\nbasic information (setAvailabilityTime)\nsummary information (setSummaryAvailabilityTime)\ndetailed information (setDetailsAvailabilityTime)\nIf the times are not explicitly set, then the following defaults are", "heading": "AsynchronousOperationExecutionOptions):", "level": 3, "chunk_index": 124, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_125", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "used:\n\nbasic information (1 year)\nsummary information (1 month)\ndetailed information (1 day)\nThe current availability of each category can be checked with\ngetAvailability, getSummaryAvailability, getDetailsAvailability methods\nof OperationExecution class. The availability can have one of the", "heading": "used:", "level": 3, "chunk_index": 125, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_126", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "following values:\n\nAVAILABLE - an information is available and can be fetched\nDELETE_PENDING - an explicit request to delete the information has\nbeen made with updateOperationExecutions or\ndeleteOperationExecutions method\nDELETED - an explicit request to delete the information has been\nprocessed and the information has been deleted\nTIME_OUT_PENDING - an availability time has expired, the\ninformation has been scheduled to be removed\nTIMED_OUT - an availability time has expired, the information has\nbeen removed\nUpdate of availability values and deletion of operation execution\nrelated information are done with two separate V3 maintenance tasks\n(please check service.properties for their configuration).\nV3GetOperationExecutionsAsynchronous.java\nimport\njava.util.Arrays\n;\nimport\njava.util.Map\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.entitytype.id.EntityTypePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.experiment.id.ExperimentIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.AsynchronousOperationExecutionOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.AsynchronousOperationExecutionResults\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.OperationExecution\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.fetchoptions.OperationExecutionFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.id.IOperationExecutionId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.CreateSamplesOperation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.SampleCreation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.id.SpacePermId\n;\npublic\nclass\nV3GetOperationExecutionsAsynchronous\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleCreation\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "following values:", "level": 3, "chunk_index": 126, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_127", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\nCreateSamplesOperation\noperation\n=\nnew\nCreateSamplesOperation\n(\nsample\n);\n// Asynchronous execution: information about an asynchronous operation execution is always gathered, the executionId\n// is also always automatically generated and returned with AsynchronousOperationExecutionResults.\nAsynchronousOperationExecutionOptions\noptions\n=\nnew\nAsynchronousOperationExecutionOptions\n();\n// Both synchronous and asynchronous executions: default availability times can be overwritten using the options object.\n// Availability times should be specified in seconds.\noptions\n.\nsetAvailabilityTime\n(\n30\n*\n24\n*\n60\n*\n60\n);\n// one month\noptions\n.\nsetSummaryAvailabilityTime\n(\n24\n*\n60\n*\n60\n);\n// one day\noptions\n.\nsetDetailsAvailabilityTime\n(\n60\n*\n60\n);\n// one hour\n// Execute operation\nAsynchronousOperationExecutionResults\nresults\n=\n(\nAsynchronousOperationExecutionResults\n)\nv3\n.\nexecuteOperations\n(\nsessionToken\n,\nArrays\n.\nasList\n(\noperation\n),\noptions\n);\n// It is an asynchronous execution. It might be still waiting for a free thread,\n// it may be already executing or it may have already finished. It does not matter.\n// We can already fetch the information about it.\n// Specify what information to fetch about the execution\nOperationExecutionFetchOptions\nfo\n=\nnew\nOperationExecutionFetchOptions\n();\nfo\n.\nwithSummary\n();\nfo\n.\nwithSummary\n().\nwithOperations\n();\nfo\n.\nwithSummary\n().\nwithProgress\n();\nfo\n.\nwithSummary\n().\nwithResults\n();\nfo\n.\nwithSummary\n().\nwithError\n();\nfo\n.\nwithDetails\n();\nfo\n.\nwithDetails\n().\nwithOperations\n();\nfo\n.\nwithDetails\n().\nwithProgress\n();\nfo\n.\nwithDetails\n().\nwithResults\n();\nfo\n.\nwithDetails\n().\nwithError\n();\n// Get information about the execution\nMap\n<\nIOperationExecutionId\n,\nOperationExecution\n>\nexecutions\n=\nv3\n.\ngetOperationExecutions\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nresults\n.\ngetExecutionId\n()),\nfo\n);\nOperationExecution\nexecution\n=\nexecutions\n.\nget\n(\nresults\n.\ngetExecutionId\n());\n// Summary contains String representation of operations, progress, results and error\nString\nsummaryOperation\n=\nexecution\n.\ngetSummary\n().\ngetOperations\n().\nget\n(\n0\n);\nSystem\n.\nout\n.\nprintln\n(\n\"Summary.operation: \"\n+\nsummaryOperation\n);\nSystem\n.\nout\n.\nprintln\n(\n\"Summary.progress: \"\n+\nexecution\n.\ngetSummary\n().\ngetProgress\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Summary.results: \"\n+\nexecution\n.\ngetSummary\n().\ngetResults\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Summary.error: \"\n+\nexecution\n.\ngetSummary\n().\ngetError\n());\n// Details contain object representation of operations, progress, results and error\nCreateSamplesOperation\ndetailsOperation\n=\n(\nCreateSamplesOperation\n)\nexecution\n.\ngetDetails\n().\ngetOperations\n().\nget\n(\n0\n);\nSystem\n.\nout\n.\nprintln\n(\n\"Details.operation: \"\n+\ndetailsOperation\n);\nSystem\n.\nout\n.\nprintln\n(\n\"Details.progress: \"\n+\nexecution\n.\ngetSummary\n().\ngetProgress\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Details.results: \"\n+\nexecution\n.\ngetSummary\n().\ngetResults\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Details.error: \"\n+\nexecution\n.\ngetSummary\n().\ngetError\n());\n}\n}\nV3GetOperationExecutionsAsynchronous.html\n<\nscript\n>\nrequire\n([\n\"openbis\"\n,\n\"as/dto/sample/create/SampleCreation\"\n,\n\"as/dto/entitytype/id/EntityTypePermId\"\n,\n\"as/dto/space/id/SpacePermId\"\n,\n\"as/dto/experiment/id/ExperimentIdentifier\"\n,\n\"as/dto/sample/create/CreateSamplesOperation\"\n,\n\"as/dto/operation/AsynchronousOperationExecutionOptions\"\n,\n\"as/dto/operation/fetchoptions/OperationExecutionFetchOptions\"\n,\n\"as/dto/operation/id/OperationExecutionPermId\"\n],\nfunction\n(\nopenbis\n,\nSampleCreation\n,\nEntityTypePermId\n,\nSpacePermId\n,\nExperimentIdentifier\n,\nCreateSamplesOperation\n,\nAsynchronousOperationExecutionOptions\n,\nOperationExecutionFetchOptions\n,\nOperationExecutionPermId\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 127, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_128", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\nvar\noperation\n=\nnew\nCreateSamplesOperation\n([\nsample\n]);\n// Asynchronous execution: information about an asynchronous operation execution is always gathered, the executionId\n// is also always automatically generated and returned with AsynchronousOperationExecutionResults.\nvar\noptions\n=\nnew\nAsynchronousOperationExecutionOptions\n();\n// Both synchronous and asynchronous executions: default availability times can be overwritten using the options object.\n// Availability times should be specified in seconds.\noptions\n.\nsetAvailabilityTime\n(\n30\n*\n24\n*\n60\n*\n60\n);\n// one month\noptions\n.\nsetSummaryAvailabilityTime\n(\n24\n*\n60\n*\n60\n);\n// one day\noptions\n.\nsetDetailsAvailabilityTime\n(\n60\n*\n60\n);\n// one hour\n// Execute operation\nv3\n.\nexecuteOperations\n([\noperation\n],\noptions\n).\ndone\n(\nfunction\n(\nresults\n)\n{\n// It is an asynchronous execution. It might be still waiting for a free thread,\n// it may be already executing or it may have already finished. It does not matter.\n// We can already fetch the information about it.\n// Specify what information to fetch about the execution\nvar\nfo\n=\nnew\nOperationExecutionFetchOptions\n();\nfo\n.\nwithSummary\n();\nfo\n.\nwithSummary\n().\nwithOperations\n();\nfo\n.\nwithSummary\n().\nwithProgress\n();\nfo\n.\nwithSummary\n().\nwithResults\n();\nfo\n.\nwithSummary\n().\nwithError\n();\nfo\n.\nwithDetails\n();\nfo\n.\nwithDetails\n().\nwithOperations\n();\nfo\n.\nwithDetails\n().\nwithProgress\n();\nfo\n.\nwithDetails\n().\nwithResults\n();\nfo\n.\nwithDetails\n().\nwithError\n();\n// Get information about the execution\nv3\n.\ngetOperationExecutions\n([\nresults\n.\ngetExecutionId\n()\n],\nfo\n).\ndone\n(\nfunction\n(\nexecutions\n)\n{\nvar\nexecution\n=\nexecutions\n[\nresults\n.\ngetExecutionId\n()];\n// Summary contains String representation of operations, progress, results and error\nvar\nsummaryOperation\n=\nexecution\n.\ngetSummary\n().\ngetOperations\n()[\n0\n];\nconsole\n.\nlog\n(\n\"Summary.operation: \"\n+\nsummaryOperation\n);\nconsole\n.\nlog\n(\n\"Summary.progress: \"\n+\nexecution\n.\ngetSummary\n().\ngetProgress\n());\nconsole\n.\nlog\n(\n\"Summary.results: \"\n+\nexecution\n.\ngetSummary\n().\ngetResults\n());\nconsole\n.\nlog\n(\n\"Summary.error: \"\n+\nexecution\n.\ngetSummary\n().\ngetError\n());\n// Details contain object representation of operations, progress, results and error\nvar\ndetailsOperation\n=\nexecution\n.\ngetDetails\n().\ngetOperations\n()[\n0\n];\nconsole\n.\nlog\n(\n\"Details.operation: \"\n+\ndetailsOperation\n);\nconsole\n.\nlog\n(\n\"Details.progress: \"\n+\nexecution\n.\ngetSummary\n().\ngetProgress\n());\nconsole\n.\nlog\n(\n\"Details.results: \"\n+\nexecution\n.\ngetSummary\n().\ngetResults\n());\nconsole\n.\nlog\n(\n\"Details.error: \"\n+\nexecution\n.\ngetSummary\n().\ngetError\n());\n});\n});\n});\n</\nscript\n>\nV3GetOperationExecutionsSynchronous.java\nimport\njava.util.Arrays\n;\nimport\njava.util.Map\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.entitytype.id.EntityTypePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.experiment.id.ExperimentIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.OperationExecution\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.SynchronousOperationExecutionOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.fetchoptions.OperationExecutionFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.id.IOperationExecutionId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.id.OperationExecutionPermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.CreateSamplesOperation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.SampleCreation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.id.SpacePermId\n;\npublic\nclass\nV3GetOperationExecutionsSynchronous\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleCreation\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 128, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_129", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE_7\"\n\n);\nCreateSamplesOperation\noperation\n=\nnew\nCreateSamplesOperation\n(\nsample\n);\n// Synchronous execution: to gather information about a synchronous operation execution, the executionId has to\n// be explicitly set in the options object. OperationExecutionPermId created with no-argument constructor automatically\n// generates a random permId value.\nSynchronousOperationExecutionOptions\noptions\n=\nnew\nSynchronousOperationExecutionOptions\n();\noptions\n.\nsetExecutionId\n(\nnew\nOperationExecutionPermId\n());\n// Both synchronous and asynchronous executions: default availability times can be overwritten using the options object.\n// Availability times should be specified in seconds.\noptions\n.\nsetAvailabilityTime\n(\n30\n*\n24\n*\n60\n*\n60\n);\n// one month\noptions\n.\nsetSummaryAvailabilityTime\n(\n24\n*\n60\n*\n60\n);\n// one day\noptions\n.\nsetDetailsAvailabilityTime\n(\n60\n*\n60\n);\n// one hour\n// Execute operation\nv3\n.\nexecuteOperations\n(\nsessionToken\n,\nArrays\n.\nasList\n(\noperation\n),\noptions\n);\n// Specify what information to fetch about the execution\nOperationExecutionFetchOptions\nfo\n=\nnew\nOperationExecutionFetchOptions\n();\nfo\n.\nwithSummary\n();\nfo\n.\nwithSummary\n().\nwithOperations\n();\nfo\n.\nwithSummary\n().\nwithProgress\n();\nfo\n.\nwithSummary\n().\nwithResults\n();\nfo\n.\nwithSummary\n().\nwithError\n();\nfo\n.\nwithDetails\n();\nfo\n.\nwithDetails\n().\nwithOperations\n();\nfo\n.\nwithDetails\n().\nwithProgress\n();\nfo\n.\nwithDetails\n().\nwithResults\n();\nfo\n.\nwithDetails\n().\nwithError\n();\n// Get information about the execution\nMap\n<\nIOperationExecutionId\n,\nOperationExecution\n>\nexecutions\n=\nv3\n.\ngetOperationExecutions\n(\nsessionToken\n,\nArrays\n.\nasList\n(\noptions\n.\ngetExecutionId\n()),\nfo\n);\nOperationExecution\nexecution\n=\nexecutions\n.\nget\n(\noptions\n.\ngetExecutionId\n());\n// Summary contains String representation of operations, progress, results and error\nString\nsummaryOperation\n=\nexecution\n.\ngetSummary\n().\ngetOperations\n().\nget\n(\n0\n);\nSystem\n.\nout\n.\nprintln\n(\n\"Summary.operation: \"\n+\nsummaryOperation\n);\nSystem\n.\nout\n.\nprintln\n(\n\"Summary.progress: \"\n+\nexecution\n.\ngetSummary\n().\ngetProgress\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Summary.results: \"\n+\nexecution\n.\ngetSummary\n().\ngetResults\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Summary.error: \"\n+\nexecution\n.\ngetSummary\n().\ngetError\n());\n// Details contain object representation of operations, progress, results and error\nCreateSamplesOperation\ndetailsOperation\n=\n(\nCreateSamplesOperation\n)\nexecution\n.\ngetDetails\n().\ngetOperations\n().\nget\n(\n0\n);\nSystem\n.\nout\n.\nprintln\n(\n\"Details.operation: \"\n+\ndetailsOperation\n);\nSystem\n.\nout\n.\nprintln\n(\n\"Details.progress: \"\n+\nexecution\n.\ngetSummary\n().\ngetProgress\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Details.results: \"\n+\nexecution\n.\ngetSummary\n().\ngetResults\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Details.error: \"\n+\nexecution\n.\ngetSummary\n().\ngetError\n());\n}\n}\nV3GetOperationExecutionsSynchronous.html\n<\nscript\n>\nrequire\n([\n\"openbis\"\n,\n\"as/dto/sample/create/SampleCreation\"\n,\n\"as/dto/entitytype/id/EntityTypePermId\"\n,\n\"as/dto/space/id/SpacePermId\"\n,\n\"as/dto/experiment/id/ExperimentIdentifier\"\n,\n\"as/dto/sample/create/CreateSamplesOperation\"\n,\n\"as/dto/operation/SynchronousOperationExecutionOptions\"\n,\n\"as/dto/operation/fetchoptions/OperationExecutionFetchOptions\"\n,\n\"as/dto/operation/id/OperationExecutionPermId\"\n],\nfunction\n(\nopenbis\n,\nSampleCreation\n,\nEntityTypePermId\n,\nSpacePermId\n,\nExperimentIdentifier\n,\nCreateSamplesOperation\n,\nSynchronousOperationExecutionOptions\n,\nOperationExecutionFetchOptions\n,\nOperationExecutionPermId\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_SAMPLE_CODE_7\"", "level": 2, "chunk_index": 129, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_130", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\nvar\noperation\n=\nnew\nCreateSamplesOperation\n([\nsample\n]);\n// Synchronous execution: to gather information about a synchronous operation execution, the executionId has to\n// be explicitly set in the options object. OperationExecutionPermId created with no-argument constructor automatically\n// generates a random permId value.\nvar\noptions\n=\nnew\nSynchronousOperationExecutionOptions\n();\noptions\n.\nsetExecutionId\n(\nnew\nOperationExecutionPermId\n());\n// Both synchronous and asynchronous executions: default availability times can be overwritten using the options object.\n// Availability times should be specified in seconds.\noptions\n.\nsetAvailabilityTime\n(\n30\n*\n24\n*\n60\n*\n60\n);\n// one month\noptions\n.\nsetSummaryAvailabilityTime\n(\n24\n*\n60\n*\n60\n);\n// one day\noptions\n.\nsetDetailsAvailabilityTime\n(\n60\n*\n60\n);\n// one hour\n// Execute operation\nv3\n.\nexecuteOperations\n([\noperation\n],\noptions\n).\ndone\n(\nfunction\n()\n{\n// Specify what information to fetch about the execution\nvar\nfo\n=\nnew\nOperationExecutionFetchOptions\n();\nfo\n.\nwithSummary\n();\nfo\n.\nwithSummary\n().\nwithOperations\n();\nfo\n.\nwithSummary\n().\nwithProgress\n();\nfo\n.\nwithSummary\n().\nwithResults\n();\nfo\n.\nwithSummary\n().\nwithError\n();\nfo\n.\nwithDetails\n();\nfo\n.\nwithDetails\n().\nwithOperations\n();\nfo\n.\nwithDetails\n().\nwithProgress\n();\nfo\n.\nwithDetails\n().\nwithResults\n();\nfo\n.\nwithDetails\n().\nwithError\n();\n// Get information about the execution\nv3\n.\ngetOperationExecutions\n([\noptions\n.\ngetExecutionId\n()\n],\nfo\n).\ndone\n(\nfunction\n(\nexecutions\n)\n{\nvar\nexecution\n=\nexecutions\n[\noptions\n.\ngetExecutionId\n()];\n// Summary contains String representation of operations, progress, results and error\nvar\nsummaryOperation\n=\nexecution\n.\ngetSummary\n().\ngetOperations\n()[\n0\n];\nconsole\n.\nlog\n(\n\"Summary.operation: \"\n+\nsummaryOperation\n);\nconsole\n.\nlog\n(\n\"Summary.progress: \"\n+\nexecution\n.\ngetSummary\n().\ngetProgress\n());\nconsole\n.\nlog\n(\n\"Summary.results: \"\n+\nexecution\n.\ngetSummary\n().\ngetResults\n());\nconsole\n.\nlog\n(\n\"Summary.error: \"\n+\nexecution\n.\ngetSummary\n().\ngetError\n());\n// Details contain object representation of operations, progress, results and error\nvar\ndetailsOperation\n=\nexecution\n.\ngetDetails\n().\ngetOperations\n()[\n0\n];\nconsole\n.\nlog\n(\n\"Details.operation: \"\n+\ndetailsOperation\n);\nconsole\n.\nlog\n(\n\"Details.progress: \"\n+\nexecution\n.\ngetSummary\n().\ngetProgress\n());\nconsole\n.\nlog\n(\n\"Details.results: \"\n+\nexecution\n.\ngetSummary\n().\ngetResults\n());\nconsole\n.\nlog\n(\n\"Details.error: \"\n+\nexecution\n.\ngetSummary\n().\ngetError\n());\n});\n});\n});\n</\nscript\n>\nMethod updateOperationExecutions / deleteOperationExecutions\n\nThe updateOperationExecutions and deleteOperationExecutions methods can\nbe used to explicitly delete some part of information or delete all the\ninformation about a given operation execution before a corresponding\navailability time expires.\nV3UpdateOperationExecutions.java\nimport\njava.util.Arrays\n;\nimport\njava.util.Map\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.entitytype.id.EntityTypePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.experiment.id.ExperimentIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.AsynchronousOperationExecutionOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.AsynchronousOperationExecutionResults\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.OperationExecution\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.fetchoptions.OperationExecutionFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.id.IOperationExecutionId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.update.OperationExecutionUpdate\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.CreateSamplesOperation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.SampleCreation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.id.SpacePermId\n;\npublic\nclass\nV3UpdateOperationExecutions\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleCreation\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 130, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_131", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\nCreateSamplesOperation\noperation\n=\nnew\nCreateSamplesOperation\n(\nsample\n);\nAsynchronousOperationExecutionOptions\noptions\n=\nnew\nAsynchronousOperationExecutionOptions\n();\n// Execute operation\nAsynchronousOperationExecutionResults\nresults\n=\n(\nAsynchronousOperationExecutionResults\n)\nv3\n.\nexecuteOperations\n(\nsessionToken\n,\nArrays\n.\nasList\n(\noperation\n),\noptions\n);\n// You can explicitly request a deletion of summary or details. Here we want to delete details.\nOperationExecutionUpdate\nupdate\n=\nnew\nOperationExecutionUpdate\n();\nupdate\n.\nsetExecutionId\n(\nresults\n.\ngetExecutionId\n());\nupdate\n.\ndeleteDetails\n();\nv3\n.\nupdateOperationExecutions\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nupdate\n));\n// Let's check the execution information\nOperationExecutionFetchOptions\nfo\n=\nnew\nOperationExecutionFetchOptions\n();\nfo\n.\nwithSummary\n();\nfo\n.\nwithDetails\n();\nMap\n<\nIOperationExecutionId\n,\nOperationExecution\n>\nexecutions\n=\nv3\n.\ngetOperationExecutions\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nresults\n.\ngetExecutionId\n()),\nfo\n);\nOperationExecution\nexecution\n=\nexecutions\n.\nget\n(\nresults\n.\ngetExecutionId\n());\n// Summary availability is AVAILABLE. Details availability is either DELETE_PENDING or DELETED\n// depending on whether a maintenance task has already processed the deletion request.\nSystem\n.\nout\n.\nprintln\n(\n\"Summary: \"\n+\nexecution\n.\ngetSummary\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Summary.availability: \"\n+\nexecution\n.\ngetSummaryAvailability\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Details: \"\n+\nexecution\n.\ngetDetails\n());\nSystem\n.\nout\n.\nprintln\n(\n\"Details.availability: \"\n+\nexecution\n.\ngetDetailsAvailability\n());\n}\n}\nV3UpdateOperationExecutions.html\n<\nscript\n>\nrequire\n([\n\"openbis\"\n,\n\"as/dto/sample/create/SampleCreation\"\n,\n\"as/dto/entitytype/id/EntityTypePermId\"\n,\n\"as/dto/space/id/SpacePermId\"\n,\n\"as/dto/experiment/id/ExperimentIdentifier\"\n,\n\"as/dto/sample/create/CreateSamplesOperation\"\n,\n\"as/dto/operation/AsynchronousOperationExecutionOptions\"\n,\n\"as/dto/operation/update/OperationExecutionUpdate\"\n,\n\"as/dto/operation/fetchoptions/OperationExecutionFetchOptions\"\n],\nfunction\n(\nopenbis\n,\nSampleCreation\n,\nEntityTypePermId\n,\nSpacePermId\n,\nExperimentIdentifier\n,\nCreateSamplesOperation\n,\nAsynchronousOperationExecutionOptions\n,\nOperationExecutionUpdate\n,\nOperationExecutionFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 131, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_132", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\nvar\noperation\n=\nnew\nCreateSamplesOperation\n([\nsample\n]);\nvar\noptions\n=\nnew\nAsynchronousOperationExecutionOptions\n();\n// Execute operation\nv3\n.\nexecuteOperations\n([\noperation\n],\noptions\n).\ndone\n(\nfunction\n(\nresults\n)\n{\n// You can explicitly request a deletion of summary or details. Here we want to delete details.\nvar\nupdate\n=\nnew\nOperationExecutionUpdate\n();\nupdate\n.\nsetExecutionId\n(\nresults\n.\ngetExecutionId\n());\nupdate\n.\ndeleteDetails\n();\nv3\n.\nupdateOperationExecutions\n([\nupdate\n]).\ndone\n(\nfunction\n()\n{\n// Let's check the execution information\nvar\nfo\n=\nnew\nOperationExecutionFetchOptions\n();\nfo\n.\nwithSummary\n();\nfo\n.\nwithDetails\n();\nv3\n.\ngetOperationExecutions\n([\nresults\n.\ngetExecutionId\n()\n],\nfo\n).\ndone\n(\nfunction\n(\nexecutions\n)\n{\nvar\nexecution\n=\nexecutions\n[\nresults\n.\ngetExecutionId\n()];\n// Summary availability is AVAILABLE. Details availability is either DELETE_PENDING or DELETED\n// depending on whether a maintenance task has already processed the deletion request.\nconsole\n.\nlog\n(\n\"Summary: \"\n+\nexecution\n.\ngetSummary\n());\nconsole\n.\nlog\n(\n\"Summary.availability: \"\n+\nexecution\n.\ngetSummaryAvailability\n());\nconsole\n.\nlog\n(\n\"Details: \"\n+\nexecution\n.\ngetDetails\n());\nconsole\n.\nlog\n(\n\"Details.availability: \"\n+\nexecution\n.\ngetDetailsAvailability\n());\n});\n});\n});\n});\n</\nscript\n>\nV3DeleteOperationExecutions.java\nimport\njava.util.Arrays\n;\nimport\njava.util.Map\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.entitytype.id.EntityTypePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.experiment.id.ExperimentIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.AsynchronousOperationExecutionOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.AsynchronousOperationExecutionResults\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.OperationExecution\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.delete.OperationExecutionDeletionOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.fetchoptions.OperationExecutionFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.operation.id.IOperationExecutionId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.CreateSamplesOperation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.create.SampleCreation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.space.id.SpacePermId\n;\npublic\nclass\nV3DeleteOperationExecutions\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nSampleCreation\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 132, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_133", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\nCreateSamplesOperation\noperation\n=\nnew\nCreateSamplesOperation\n(\nsample\n);\nAsynchronousOperationExecutionOptions\noptions\n=\nnew\nAsynchronousOperationExecutionOptions\n();\n// Execute operation\nAsynchronousOperationExecutionResults\nresults\n=\n(\nAsynchronousOperationExecutionResults\n)\nv3\n.\nexecuteOperations\n(\nsessionToken\n,\nArrays\n.\nasList\n(\noperation\n),\noptions\n);\n// Explicitly request a deletion of all the information about the execution\nOperationExecutionDeletionOptions\ndeletionOptions\n=\nnew\nOperationExecutionDeletionOptions\n();\ndeletionOptions\n.\nsetReason\n(\n\"test reason\"\n);\nv3\n.\ndeleteOperationExecutions\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nresults\n.\ngetExecutionId\n()),\ndeletionOptions\n);\n// Let's check whether the execution information is still available\nMap\n<\nIOperationExecutionId\n,\nOperationExecution\n>\nexecutions\n=\nv3\n.\ngetOperationExecutions\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nresults\n.\ngetExecutionId\n()),\nnew\nOperationExecutionFetchOptions\n());\nOperationExecution\nexecution\n=\nexecutions\n.\nget\n(\nresults\n.\ngetExecutionId\n());\n// Depending on whether a maintenance task has already processed the deletion request\n// the execution will be either null or the returned execution availability will be DELETE_PENDING.\nSystem\n.\nout\n.\nprintln\n(\n\"Availability: \"\n+\n(\nexecution\n!=\nnull\n?\nexecution\n.\ngetAvailability\n()", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 133, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_134", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nnull\n));\n}\n}\nV3DeleteOperationExecutions.html\n<\nscript\n>\nrequire\n([\n\"openbis\"\n,\n\"as/dto/sample/create/SampleCreation\"\n,\n\"as/dto/entitytype/id/EntityTypePermId\"\n,\n\"as/dto/space/id/SpacePermId\"\n,\n\"as/dto/experiment/id/ExperimentIdentifier\"\n,\n\"as/dto/sample/create/CreateSamplesOperation\"\n,\n\"as/dto/operation/AsynchronousOperationExecutionOptions\"\n,\n\"as/dto/operation/delete/OperationExecutionDeletionOptions\"\n,\n\"as/dto/operation/fetchoptions/OperationExecutionFetchOptions\"\n],\nfunction\n(\nopenbis\n,\nSampleCreation\n,\nEntityTypePermId\n,\nSpacePermId\n,\nExperimentIdentifier\n,\nCreateSamplesOperation\n,\nAsynchronousOperationExecutionOptions\n,\nOperationExecutionDeletionOptions\n,\nOperationExecutionFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nsample\n=\nnew\nSampleCreation\n();\nsample\n.\nsetTypeId\n(\nnew\nEntityTypePermId\n(", "heading": ":", "level": 3, "chunk_index": 134, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_135", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_SAMPLE_CODE\"\n\n);\nvar\noperation\n=\nnew\nCreateSamplesOperation\n([\nsample\n]);\nvar\noptions\n=\nnew\nAsynchronousOperationExecutionOptions\n();\n// Execute operation\nv3\n.\nexecuteOperations\n([\noperation\n],\noptions\n).\ndone\n(\nfunction\n(\nresults\n)\n{\n// Explicitly request a deletion of all the information about the execution\nvar\ndeletionOptions\n=\nnew\nOperationExecutionDeletionOptions\n();\ndeletionOptions\n.\nsetReason\n(\n\"test reason\"\n);\nv3\n.\ndeleteOperationExecutions\n([\nresults\n.\ngetExecutionId\n()\n],\ndeletionOptions\n).\ndone\n(\nfunction\n()\n{\n// Let's check whether the execution information is still available\nv3\n.\ngetOperationExecutions\n([\nresults\n.\ngetExecutionId\n()\n],\nnew\nOperationExecutionFetchOptions\n()).\ndone\n(\nfunction\n(\nexecutions\n)\n{\nvar\nexecution\n=\nexecutions\n[\nresults\n.\ngetExecutionId\n()];\n// Depending on whether a maintenance task has already processed the deletion request\n// the execution will be either null or the returned execution availability will be DELETE_PENDING.\nconsole\n.\nlog\n(\n\"Availability: \"\n+\n(\nexecution\n!=\nnull\n?\nexecution\n.\ngetAvailability\n()", "heading": "\"MY_SAMPLE_CODE\"", "level": 2, "chunk_index": 135, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_136", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nnull\n));\n});\n});\n});\n});\n</\nscript\n>\nConfiguration\n\nMany aspects of the operation execution behavior can be configured via\nservice.properties file.\nMore details on what exactly can be configured can be found in the file\nitself.", "heading": ":", "level": 3, "chunk_index": 136, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_137", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "recommended to read the following tutorial first:\n\nhttp://www.linkeddatatools.com/semantic-web-basics\n.\nIn short: semantic annotations allow you to define a meaning for openBIS\nsample types, property types and sample property assignments by the\nmeans of ontology terms. This, together with standards like “Dublin\nCore” (\nhttp://dublincore.org/\n) can help you integrate openBIS with\nother systems and exchange data between them with a well defined meaning\neasily.\nTo describe a meaning of a single sample type, property type or sample\nproperty assignment a collection of semantic annotations can be used.\nTherefore, for instance, you can use one annotation to describe a\ngeneral meaning of a property and another one to describe a unit that is\nused for its values.\nIn order to make the openBIS configuration easier to maintain sample\nproperty assignments inherit semantic annotations from a corresponding\nproperty type. This inheritance works only for sample property\nassignments without any semantic annotations, i.e. if there is at least\none semantic annotation defined at a sample property assignment level\nthen nothing gets inherited from the property type level anymore. The\ninheritance makes it possible to define a meaning of a property once, at\nthe property type level, and override it, only if needed, at sample\nproperty assignment level.\nV3 API provides the following methods to manipulate the semantic", "heading": "recommended to read the following tutorial first:", "level": 3, "chunk_index": 137, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_138", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "annotations:\n\ncreateSemanticAnnotations\nupdateSemanticAnnotations\ndeleteSemanticAnnotations\ngetSemanticAnnotations\nsearchSemanticAnnotations\nThese methods work similar to the other create/update/delete/get/search\nV3 API counterparts.\nMoreover, once semantic annotations are defined, it is possible to\nsearch for samples and sample types that have a given semantic\nannotation. To do it, one has to use searchSamples and searchSampleTypes\nmethods and specify appropriate withType().withSemanticAnnotations()\ncondition in SampleSearchCriteria or withSemanticAnnotations() condition\nin SampleTypeSearchCriteria.", "heading": "annotations:", "level": 3, "chunk_index": 138, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_139", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Web App Settings\n\n\nThe web app settings functionality is a user specific key-value map\nwhere a user specific configuration can be stored. The settings are\npersistent, i.e. they can live longer than a user session that created\nthem. Web app settings of a given user can be read/updated only by that\nuser or by an instance admin.\nWebAppSettingsExample.java\nimport\njava.util.Arrays\n;\nimport\njava.util.Map\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.person.Person\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.person.fetchoptions.PersonFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.person.id.IPersonId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.person.id.Me\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.person.update.PersonUpdate\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.webapp.WebAppSetting\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.webapp.create.WebAppSettingCreation\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.webapp.fetchoptions.WebAppSettingsFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.webapp.update.WebAppSettingsUpdateValue\n;\npublic\nclass\nWebAppSettingsExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nPersonUpdate\nupdate\n=\nnew\nPersonUpdate\n();\n// update the currently logged in user\nupdate\n.\nsetUserId\n(\nnew\nMe\n());\n// add \"setting1a\" and \"setting1b\" to \"app1\" (other settings for \"app1\" will remain unchanged)\nWebAppSettingsUpdateValue\napp1\n=\nupdate\n.\ngetWebAppSettings\n(\n\"app1\"\n);\napp1\n.\nadd\n(\nnew\nWebAppSettingCreation\n(\n\"setting1a\"\n,\n\"value1a\"\n));\napp1\n.\nadd\n(\nnew\nWebAppSettingCreation\n(\n\"setting1b\"\n,\n\"value1b\"\n));\n// set \"setting2a\", \"setting2b\" and \"setting2c\" for \"app2\" (other settings for \"app2\" will be removed)\nWebAppSettingsUpdateValue\napp2\n=\nupdate\n.\ngetWebAppSettings\n(\n\"app2\"\n);\napp2\n.\nset\n(\nnew\nWebAppSettingCreation\n(\n\"setting2a\"\n,\n\"value2a\"\n),\nnew\nWebAppSettingCreation\n(\n\"setting2b\"\n,\n\"value2b\"\n),\nnew\nWebAppSettingCreation\n(\n\"setting2c\"\n,\n\"value2c\"\n));\n// remove \"setting3a\" from \"app3\" (other settings for \"app3\" will remain unchanged)\nWebAppSettingsUpdateValue\napp3\n=\nupdate\n.\ngetWebAppSettings\n(\n\"app3\"\n);\napp3\n.\nremove\n(\n\"setting3a\"\n);\nv3\n.\nupdatePersons\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nupdate\n));\n// option 1 : fetch a person with all settings of all web apps\nPersonFetchOptions\npersonFo1\n=\nnew\nPersonFetchOptions\n();\npersonFo1\n.\nwithAllWebAppSettings\n();\n// option 2 : fetch a person with either all or chosen settings of chosen web apps\nPersonFetchOptions\npersonFo2\n=\nnew\nPersonFetchOptions\n();\n// option 2a : fetch \"app1\" with all settings\nWebAppSettingsFetchOptions\napp1Fo\n=\npersonFo2\n.\nwithWebAppSettings\n(\n\"app1\"\n);\napp1Fo\n.\nwithAllSettings\n();\n// option 2b : fetch \"app2\" with chosen settings\nWebAppSettingsFetchOptions\napp2Fo\n=\npersonFo2\n.\nwithWebAppSettings\n(\n\"app2\"\n);\napp2Fo\n.\nwithSetting\n(\n\"setting2a\"\n);\napp2Fo\n.\nwithSetting\n(\n\"setting2b\"\n);\nMap\n<\nIPersonId\n,\nPerson\n>\npersons\n=\nv3\n.\ngetPersons\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nnew\nMe\n()),\npersonFo2\n);\nPerson\nperson\n=\npersons\n.\nvalues\n().\niterator\n().\nnext\n();\n// get \"setting1a\" for \"app1\"\nWebAppSetting\nsetting1a\n=\nperson\n.\ngetWebAppSettings\n(\n\"app1\"\n).\ngetSetting\n(\n\"setting1a\"\n);\nSystem\n.\nout\n.\nprintln\n(\nsetting1a\n.\ngetValue\n());\n// get all fetched settings for \"app2\"\nMap\n<\nString\n,\nWebAppSetting\n>\nsettings2\n=\nperson\n.\ngetWebAppSettings\n(\n\"app2\"\n).\ngetSettings\n();\nSystem\n.\nout\n.\nprintln\n(\nsettings2\n);\n}\n}\nWebAppSettingsExample.html\n<\nscript\n>\nrequire\n([\n\"jquery\"\n,\n\"openbis\"\n,\n\"as/dto/person/update/PersonUpdate\"\n,\n\"as/dto/person/id/Me\"\n,\n\"as/dto/webapp/create/WebAppSettingCreation\"\n,\n\"as/dto/person/fetchoptions/PersonFetchOptions\"\n],\nfunction\n(\n$\n,\nopenbis\n,\nPersonUpdate\n,\nMe\n,\nWebAppSettingCreation\n,\nPersonFetchOptions\n)\n{\n$\n(\ndocument\n).\nready\n(\nfunction\n()\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nupdate\n=\nnew\nPersonUpdate\n();\n// update the currently logged in user\nupdate\n.\nsetUserId\n(\nnew\nMe\n());\n// add \"setting1a\" and \"setting1b\" to \"app1\" (other settings for \"app1\" will remain unchanged)\nvar\napp1\n=\nupdate\n.\ngetWebAppSettings\n(\n\"app1\"\n);\napp1\n.\nadd\n(\nnew\nWebAppSettingCreation\n(\n\"setting1a\"\n,\n\"value1a\"\n));\napp1\n.\nadd\n(\nnew\nWebAppSettingCreation\n(\n\"setting1b\"\n,\n\"value1b\"\n));\n// set \"setting2a\", \"setting2b\" and \"setting2c\" for \"app2\" (other settings for \"app2\" will be removed)\nvar\napp2\n=\nupdate\n.\ngetWebAppSettings\n(\n\"app2\"\n);\napp2\n.\nset\n([\nnew\nWebAppSettingCreation\n(\n\"setting2a\"\n,\n\"value2a\"\n),\nnew\nWebAppSettingCreation\n(\n\"setting2b\"\n,\n\"value2b\"\n),\nnew\nWebAppSettingCreation\n(\n\"setting2c\"\n,\n\"value2c\"\n)\n]);\n// remove \"setting3a\" from \"app3\" (other settings for \"app3\" will remain unchanged)\nvar\napp3\n=\nupdate\n.\ngetWebAppSettings\n(\n\"app3\"\n);\napp3\n.\nremove\n(\n\"setting3a\"\n);\nv3\n.\nupdatePersons\n([\nupdate\n]).\ndone\n(\nfunction\n()\n{\n// option 1 : fetch a person with all settings of all web apps\nvar\npersonFo1\n=\nnew\nPersonFetchOptions\n();\npersonFo1\n.\nwithAllWebAppSettings\n();\n// option 2 : fetch a person with either all or chosen settings of chosen web apps\nvar\npersonFo2\n=\nnew\nPersonFetchOptions\n();\n// option 2a : fetch \"app1\" with all settings\nvar\napp1Fo\n=\npersonFo2\n.\nwithWebAppSettings\n(\n\"app1\"\n);\napp1Fo\n.\nwithAllSettings\n();\n// option 2b : fetch \"app2\" with chosen settings\nvar\napp2Fo\n=\npersonFo2\n.\nwithWebAppSettings\n(\n\"app2\"\n);\napp2Fo\n.\nwithSetting\n(\n\"setting2a\"\n);\napp2Fo\n.\nwithSetting\n(\n\"setting2b\"\n);\nv3\n.\ngetPersons\n([\nnew\nMe\n()\n],\npersonFo2\n).\ndone\n(\nfunction\n(\npersons\n)\n{\nvar\nperson\n=\npersons\n[\nnew\nMe\n()];\n// get \"setting1a\" for \"app1\"\nvar\nsetting1a\n=\nperson\n.\ngetWebAppSettings\n(\n\"app1\"\n).\ngetSetting\n(\n\"setting1a\"\n);\nconsole\n.\nlog\n(\nsetting1a\n.\ngetValue\n());\n// get all fetched settings for \"app2\"\nvar\nsettings2\n=\nperson\n.\ngetWebAppSettings\n(\n\"app2\"\n).\ngetSettings\n();\nconsole\n.\nlog\n(\nsettings2\n);\n});\n});\n});\n});\n</\nscript\n>\nImports\n\nThe imports that are normally accesible via “Import” menu in the generic\nopenBIS UI can be also used programatically from within a V3 custom AS\nservice. Such an import process consists of two steps:\nuploading a file to /openbis/upload servlet to be temporarily stored\nunder a specific user session key (more information on the upload\nservlet can be found\nhere\n)\nimporting the uploaded file using one\nof ch.ethz.sis.openbis.generic.asapi.v3.plugin.service.IImportService\nmethods accessible from within a V3 custom AS service", "heading": "Web App Settings", "level": 3, "chunk_index": 139, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_140", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Currently available import methods:\n\nString createExperiments(String sessionToken, String uploadKey,\nString experimentTypeCode, boolean async, String userEmail)\nString updateExperiments(String sessionToken, String uploadKey,\nString experimentTypeCode, boolean async, String userEmail)\nString createSamples(String sessionToken, String uploadKey, String\nsampleTypeCode, String defaultSpaceIdentifier, String\nspaceIdentifierOverride, String experimentIdentifierOverride,\nboolean updateExisting, boolean async, String userEmail)\nString updateSamples(String sessionToken, String uploadKey, String\nsampleTypeCode, String defaultSpaceIdentifier, String\nspaceIdentifierOverride, String experimentIdentifierOverride,\nboolean async, String userEmail)\nString updateDataSets(String sessionToken, String uploadKey, String\ndataSetTypeCode, boolean async, String userEmail)\nString createMaterials(String sessionToken, String uploadKey, String\nmaterialTypeCode, boolean updateExisting, boolean async, String\nuserEmail)\nString updateMaterials(String sessionToken, String uploadKey, String\nmaterialTypeCode, boolean ignoreUnregistered, boolean async, String\nuserEmail)\nString generalImport(String sessionToken, String uploadKey, String\ndefaultSpaceIdentifier, boolean updateExisting,\nboolean async, String userEmail) - import of samples and materials\nfrom an Excel file\nString customImport(String sessionToken, String uploadKey, String\ncustomImportCode, boolean async, String userEmail) - import\ndelegated to a dropbox", "heading": "Currently available import methods:", "level": 3, "chunk_index": 140, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_141", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Parameters:\n\nParameter\nType\nMethods\nDescription\nsessionToken\nString\nALL\nopenBIS session token; to get a session token of a currently logged in user inside a custom AS service context.getSessionToken() method shall be used.\nuploadKey\nString\nALL\nA key the file to be imported has been uploaded to (see the 1st step of the import process described above).\nasync\nboolean\nALL\nA flag that controls whether the import should be performed synchronously (i.e. in the current thread) or asynchronously (i.e. in a separate thread). For asynchronous imports an email with either an execution result or error is sent to the specified email address (see userEmail parameter).\nuserEmail\nString\nALL\nAn email address where an execution result or error should be sent to (only for asynchronous imports - see async parameter).\nexperimentTypeCode\nString\ncreateExperiments, updateExperiments\nA type of experiments to be created/updated.\nsampleTypeCode\nString\ncreateSamples, updateSamples\nA type of samples to be created/updated.\ndataSetTypeCode\nString\nupdateDataSets\nA type of data sets to be updated.\nmaterialTypeCode\nString\ncreateMaterials, updateMaterials\nA type of materials to be created/updated.\ncustomImportCode\nString\ncustomImport\nA code of a custom import the import process should be delegated to. A custom import sends the uploaded file to a dropbox. Inside a dropbox the uploaded file can be accessed via transaction.getIncoming() method.\ndefaultSpaceIdentifier\nString\ncreateSamples, updateSamples, generalImport\nA default space identifier. If null then identifiers of samples to be created/updated are expected to be specified in the uploaded file. If not null then:\ncodes of samples to be created are automatically generated and the samples are created in the requested default space\nidentifiers of samples to be updated can omit the space part (the requested default space will be automatically added)\nspaceIdentifierOverride\nString\ncreateSamples, updateSamples\nA space identifier to be used instead of the ones defined in the uploaded file.\nexperimentIdentifierOverride\nString\ncreateSamples, updateSamples\nAn experiment identifier to be used instead of the ones defined in the uploaded file.\nupdateExisting\nboolean\ncreateSamples, createMaterials, generalImport\nA flag that controlls whether in case of an attempt to create an already existing entity an update should be performed or such a creation should fail.\nignoreUnregistered\nboolean\nupdateMaterials\nA flag that controlls whether in case of an attempt to update a nonexistent entity such update should be silently ignored or it should fail.", "heading": "Parameters:", "level": 3, "chunk_index": 141, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_142", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "File formats:\n\nThe TSV examples below assume experiment/sample/dataset/material type\nused contains exactly one property called “DESCRIPTION”.\nMethod\nTemplate\ncreateExperiments\ncreate-experiments-import-template.tsv\nupdateExperiments\nupdate-experiments-import-template.tsv\ncreateSamples\ncreate-samples-import-template.tsv\nupdateSamples\nupdate-samples-import-template.tsv\nupdateDataSets\nupdate-data-sets-import-template.tsv\ncreateMaterials\ncreate-materials-import-template.tsv\nupdateMaterials\nupdate-materials-import-template.tsv\ngeneralImport\ncustomImport\nany kind of file", "heading": "File formats:", "level": 3, "chunk_index": 142, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_143", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Return values:\n\nAll methods return a message with a short summary of the performed\noperation, e.g. a synchronous createSamples method call could return a\nmessage like “Registration of 1 sample(s) is complete.” while the\nasynchronous version could return a message like “When the import is\ncomplete the confirmation or failure report will be sent by email.”.\nAn example webapp to upload a file with samples and a custom AS service\nto import that file is presented below.\nImportSamplesWebAppExample.html\n<!DOCTYPE html>\n<\nhtml\n>\n<\nhead\n>\n<\nmeta\ncharset\n=\n\"utf-8\"\n>\n<\ntitle\n>\nSamples import\n</\ntitle\n>\n<\nscript\ntype\n=\n\"text/javascript\"\nsrc\n=\n\"/openbis-test/resources/api/v3/config.js\"\n></\nscript\n>\n<\nscript\ntype\n=\n\"text/javascript\"\nsrc\n=\n\"/openbis-test/resources/api/v3/require.js\"\n></\nscript\n>\n</\nhead\n>\n<\nbody\n>\n<\nscript\n>\nrequire\n([\n\"jquery\"\n,\n\"openbis\"\n,\n\"as/dto/service/id/CustomASServiceCode\"\n,\n\"as/dto/service/CustomASServiceExecutionOptions\"\n],\nfunction\n(\n$\n,\nopenbis\n,\nCustomASServiceCode\n,\nCustomASServiceExecutionOptions\n)\n{\n$\n(\ndocument\n).\nready\n(\nfunction\n()\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\nuploadFrame\n=\n$\n(\n\"#uploadFrame\"\n);\nuploadFrame\n.\nload\n(\nfunction\n()\n{\nalert\n(\n\"Upload finished\"\n)\n});\nvar\nuploadForm\n=\n$\n(\n\"#uploadForm\"\n);\nuploadForm\n.\nfind\n(\n\"input[name=sessionID]\"\n).\nval\n(\nsessionToken\n);\nvar\nimportForm\n=\n$\n(\n\"#importForm\"\n);\nimportForm\n.\nsubmit\n(\nfunction\n(\ne\n)\n{\ne\n.\npreventDefault\n();\nvar\nsampleType\n=\nimportForm\n.\nfind\n(\n\"input[name=sampleType]\"\n).\nval\n();\nvar\nserviceId\n=\nnew\nCustomASServiceCode\n(\n\"import-service\"\n);\nvar\nserviceOptions\n=\nnew\nCustomASServiceExecutionOptions\n();\nserviceOptions\n.\nwithParameter\n(\n\"sampleType\"\n,\nsampleType\n);\nfacade\n.\nexecuteCustomASService\n(\nserviceId\n,\nserviceOptions\n).\ndone\n(\nfunction\n(\nresult\n)\n{\nalert\n(\n\"Import successful: \"\n+\nresult\n);\n}).\nfail\n(\nfunction\n(\nerror\n)\n{\nalert\n(\n\"Import failed: \"\n+\nerror\n.\nmessage\n);\n});\nreturn\nfalse\n;\n});\n});\n});\n</\nscript\n>\n<\niframe\nid\n=\n\"uploadFrame\"\nname\n=\n\"uploadFrame\"\nstyle\n=\n\"display: none\"\n></\niframe\n>\n<\nh1\n>\nStep 1 : upload samples file\n</\nh1\n>\n<\nform\nid\n=\n\"uploadForm\"\nmethod\n=\n\"post\"\naction\n=\n\"/openbis/upload\"\nenctype\n=\n\"multipart/form-data\"\ntarget\n=\n\"uploadFrame\"\n>\n<\ninput\ntype\n=\n\"file\"\nname\n=\n\"importWebappUploadKey\"\nmultiple\n=\n\"multiple\"\n>\n<\ninput\ntype\n=\n\"hidden\"\nname\n=\n\"sessionID\"\n>\n<\ninput\ntype\n=\n\"hidden\"\nname\n=\n\"sessionKeysNumber\"\nvalue\n=\n\"1\"\n>\n<\ninput\ntype\n=\n\"hidden\"\nname\n=\n\"sessionKey_0\"\nvalue\n=\n\"importWebappUploadKey\"\n>\n<\ninput\ntype\n=\n\"submit\"\n>\n</\nform\n>\n<\nh1\n>\nStep 2 : import samples file\n</\nh1\n>\n<\nform\nid\n=\n\"importForm\"\n>\n<\nlabel\n>", "heading": "Return values:", "level": 3, "chunk_index": 143, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_144", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Sample Type\n\n</\nlabel\n>\n<\ninput\ntype\n=\n\"text\"\nname\n=\n\"sampleType\"\n>\n<\ninput\ntype\n=\n\"submit\"\n>\n</\nform\n>\n</\nbody\n>\n</\nhtml\n>\nImportSamplesServiceExample.py\ndef\nprocess\n(\ncontext\n,\nparameters", "heading": "Sample Type", "level": 3, "chunk_index": 144, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_145", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "):\n\nsampleType\n=\nparameters\n.\nget\n(\n\"sampleType\"\n)\nreturn\ncontext\n.\ngetImportService\n()\n.\ncreateSamples\n(\ncontext\n.\ngetSessionToken\n(),\n\"importWebappUploadKey\"\n,\nsampleType\n,\nNone\n,\nNone\n,\nNone\n,\nFalse\n,\nFalse\n,\nNone\n);", "heading": "):", "level": 3, "chunk_index": 145, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_146", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "### Generate identifiers\n\nV3 API provides 2 methods for generating unique identifiers:\ncreatePermIdStrings - generates globally unique identifiers that\nconsist of a timestamp and a sequence generated number (e.g.\n“20180531170854641-944”); this method uses one global sequence.\ncreateCodes - generates identifiers that are unique for a given\nentity kind and consist of a prefix and a sequence generated number\n(e.g. “MY-PREFIX-147”); this method uses a dedicated sequence for\neach entity kind.\nGenerateIdentifiersExample.java\nimport\njava.util.List\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.entitytype.EntityKind\n;\npublic\nclass\nGenerateIdentifiersExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nList\n<\nString\n>\npermIds\n=\nv3\n.\ncreatePermIdStrings\n(\nsessionToken\n,\n2\n);\nList\n<\nString\n>\ncodes\n=\nv3\n.\ncreateCodes\n(\nsessionToken\n,", "heading": "### Generate identifiers", "level": 3, "chunk_index": 146, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_147", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SAMPLE\n\n,\n3\n);\nSystem\n.\nout\n.\nprintln\n(\npermIds\n);\n// example output: [20180531170854641-944, 20180531170854641-945]\nSystem\n.\nout\n.\nprintln\n(\ncodes\n);\n// example output: [MY-PREFIX-782, MY-PREFIX-783, MY-PREFIX-784]\n}\n}\nGenerateIdentifiersExample.html\n<\nscript\n>\nrequire\n([\n\"jquery\"\n,\n\"openbis\"\n,\n\"as/dto/entitytype/EntityKind\"\n],\nfunction\n(\n$\n,\nopenbis\n,\nEntityKind\n)\n{\n$\n(\ndocument\n).\nready\n(\nfunction\n()\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nv3\n.\ncreatePermIdStrings\n(\n2\n).\nthen\n(\nfunction\n(\npermIds\n)\n{\nconsole\n.\nlog\n(\npermIds\n);\n// example output: [20180531170854641-944, 20180531170854641-945]\n});\nv3\n.\ncreateCodes\n(", "heading": "SAMPLE", "level": 2, "chunk_index": 147, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_148", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SAMPLE\n\n,\n3\n).\nthen\n(\nfunction\n(\ncodes\n)\n{\nconsole\n.\nlog\n(\ncodes\n);\n// example output: [MY-PREFIX-782, MY-PREFIX-783, MY-PREFIX-784]\n});\n});\n});\n</\nscript\n>", "heading": "SAMPLE", "level": 2, "chunk_index": 148, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_149", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "V. DSS Methods\n\n\nSearch files\n\nThe searchFiles method can be used to search for data set files at a\nsingle data store (Java version) or at multiple data stores at the same\ntime (Javascript version).\nSimilar to the other V3 search methods it takes as parameters a\nsessionToken, search criteria and fetch options and returns a search\nresult object.\nWhen searching across multiple data stores the results from each data\nstore are combined together and returned back as a single regular search\nresult object as if it was returned by only one data store.\nExample\n\nV3SearchDataSetFilesExample.java\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.search.DataSetSearchCriteria\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.DataSetFile\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.fetchoptions.DataSetFileFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.search.DataSetFileSearchCriteria\n;\npublic\nclass\nV3SearchDataSetFilesExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\n// we assume here that v3 objects for both AS and DSS have been already created and we have already called login on AS to get the sessionToken (please check \"Accessing the API\" section for more details)\nDataSetFileSearchCriteria\ncriteria\n=\nnew\nDataSetFileSearchCriteria\n();\nDataSetSearchCriteria\ndataSetCriteria\n=\ncriteria\n.\nwithDataSet\n().\nwithOrOperator\n();\ndataSetCriteria\n.\nwithCode\n().\nthatEquals\n(", "heading": "V. DSS Methods", "level": 3, "chunk_index": 149, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_150", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_DATA_SET_CODE_2\"\n\n);\n// Searches for files at at a single data store\nSearchResult\n<\nDataSetFile\n>\nresult\n=\ndssV3\n.\nsearchFiles\n(\nsessionToken\n,\ncriteria\n,\nnew\nDataSetFileFetchOptions\n());\nfor\n(\nDataSetFile\nfile", "heading": "\"MY_DATA_SET_CODE_2\"", "level": 2, "chunk_index": 150, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_151", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nresult\n.\ngetObjects\n())\n{\nSystem\n.\nout\n.\nprintln\n(\n\"DataSet: \"\n+\nfile\n.\ngetDataSetPermId\n()\n+\n\" has file: \"\n+\nfile\n.\ngetPath\n());\n}\n}\n}\nV3SearchDataSetFilesAtAllDataStoresExample.html\n<\nscript\n>\nrequire\n([\n\"openbis\"\n,\n\"dss/dto/datasetfile/search/DataSetFileSearchCriteria\"\n,\n\"dss/dto/datasetfile/fetchoptions/DataSetFileFetchOptions\"\n],\nfunction\n(\nDataSetFileSearchCriteria\n,\nDataSetFileFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\ncriteria\n=\nnew\nDataSetFileSearchCriteria\n();\nvar\ndataSetCriteria\n=\ncriteria\n.\nwithDataSet\n().\nwithOrOperator\n();\ndataSetCriteria\n.\nwithCode\n().\nthatEquals\n(", "heading": ":", "level": 3, "chunk_index": 151, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_152", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_DATA_SET_CODE_2\"\n\n);\nvar\nfetchOptions\n=\nnew\nDataSetFileFetchOptions\n();\n// getDataStoreFacade() call (without any parameters) returns a facade object that uses all available data stores,\n// e.g. calling searchFiles on such a facade searches for files at all available data stores\nv3\n.\ngetDataStoreFacade\n().\nsearchFiles\n(\ncriteria\n,\nfetchOptions\n).\ndone\n(\nfunction\n(\nresult\n)\n{\nresult\n.\ngetObjects\n().\nforEach\n(\nfunction\n(\nfile\n)\n{\nconsole\n.\nlog\n(\n\"DataSet: \"\n+\nfile\n.\ngetDataSetPermId\n()\n+\n\" has file: \"\n+\nfile\n.\ngetPath\n());\n});\n});\n});\n</\nscript\n>\nV3SearchDataSetFilesAtChosenDataStoresExample.html\n<\nscript\n>\nrequire\n([\n\"openbis\"\n,\n\"dss/dto/datasetfile/search/DataSetFileSearchCriteria\"\n,\n\"dss/dto/datasetfile/fetchoptions/DataSetFileFetchOptions\"\n],\nfunction\n(\nDataSetFileSearchCriteria\n,\nDataSetFileFetchOptions\n)\n{\n// we assume here that v3 object has been already created and we have already called login (please check \"Accessing the API\" section for more details)\nvar\ncriteria\n=\nnew\nDataSetFileSearchCriteria\n();\nvar\ndataSetCriteria\n=\ncriteria\n.\nwithDataSet\n().\nwithOrOperator\n();\ndataSetCriteria\n.\nwithCode\n().\nthatEquals\n(", "heading": "\"MY_DATA_SET_CODE_2\"", "level": 2, "chunk_index": 152, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_153", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"MY_DATA_SET_CODE_2\"\n\n);\nvar\nfetchOptions\n=\nnew\nDataSetFileFetchOptions\n();\n// getDataStoreFacade(\"DSS1\",\"DSS2\") returns a facade object that uses only \"DSS1\" and \"DSS2\" data stores,\n// e.g. calling searchFiles on such a facade searches for files only at these two data stores even if there\n// are more datastores available\nv3\n.\ngetDataStoreFacade\n(", "heading": "\"MY_DATA_SET_CODE_2\"", "level": 2, "chunk_index": 153, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_154", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"DSS2\"\n\n).\nsearchFiles\n(\ncriteria\n,\nfetchOptions\n).\ndone\n(\nfunction\n(\nresult\n)\n{\nresult\n.\ngetObjects\n().\nforEach\n(\nfunction\n(\nfile\n)\n{\nconsole\n.\nlog\n(\n\"DataSet: \"\n+\nfile\n.\ngetDataSetPermId\n()\n+\n\" has file: \"\n+\nfile\n.\ngetPath\n());\n});\n});\n});\n</\nscript\n>\nDownloading files, folders, and datasets\n\nDatasets that are created in Open BIS can be accessed by V3 API in a\nnumber of different ways. It’s possible to download individual files,\nfolders, and entire datasets as illustrated in the following examples.\nTo get started, it is necessary to reference both the AS API\n(IApplicationServerApi) and the DSS API (IDataStoreServerAPI), and login\nand get a session token object.", "heading": "\"DSS2\"", "level": 2, "chunk_index": 154, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_155", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "The API provides two methods for downloading:\n\nSimple downloading: A single InputStream is returned which contains\nall files and file meta data.\nFast downloading: A FastDownloadSession object is returned which is\nused by a helper class to download files in parallel streams in\nchunks. It is based on the", "heading": "The API provides two methods for downloading:", "level": 3, "chunk_index": 155, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_156", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Simple Downloading\n\n\nBy setting the DataSetFileDownloadOptions it’s possible to change how\ndata is downloaded - data can be downloaded file by file, or by folder,\nby an entire dataset in a recursive manner. It is also possible to\nsearch for datasets by defining the appropriate search criteria\n(DataSetFileSearchCriteria).\nIn order to download content via the V3 DSS API, the dataset needs to\nalready be inside Open BIS. It is necessary to know the dataset code at\nthe very minimum. It is helpful to also know the file path to the file\ndesired to download.\nDownload a single file located inside a dataset\n\nHere is how to download a single file and print out the contents, when\nthe dataset code and the file path are known. Here a search is not\nnecessary since the file path and dataset code are known.\nA note about recursion\n\nNote that when only downloading one file, it is better to set the\nrecursive flag to false in DataSetFileDownloadOptions, although it makes\nno difference in the results returned. The recursive flag really only\nmatters when downloading entire datasets or directories - if it is true,\nthen the entire tree of contents will be downloaded, if false, then the\nsingle path requested will be downloaded. If that path is just a\ndirectory then the returned result will consist of just meta data about\nthe directory.\nDownload a single file\nimport\njava.io.InputStream\n;\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.id.DataSetPermId\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.IDataStoreServerApi\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownload\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownloadOptions\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownloadReader\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.id.DataSetFilePermId\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.id.IDataSetFileId\n;\nimport\nch.systemsx.cisd.common.spring.HttpInvokerUtils\n;\npublic\nclass\nV3DSSExample1\n{", "heading": "Simple Downloading", "level": 3, "chunk_index": 156, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_157", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "// DATASET EXAMPLE STRUCTURE\n\n// The dataset consists of a root folder with 2 files and a subfolder with 1 file", "heading": "// DATASET EXAMPLE STRUCTURE", "level": 2, "chunk_index": 157, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_158", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "DSS_URL\n\n=\n\"https://localhost:8444/datastore_server\"\n;\n// Reference the DSS\nIDataStoreServerApi\ndss\n=\nHttpInvokerUtils\n.\ncreateStreamSupportingServiceStub\n(\nIDataStoreServerApi\n.\nclass\n,", "heading": "DSS_URL", "level": 2, "chunk_index": 158, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_159", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SERVICE_URL\n\n,\n10000\n);\n// Reference the AS and login & get a session token\nIApplicationServerApi\nas\n=\nHttpInvokerUtils\n.\ncreateServiceStub\n(\nIApplicationServerApi\n.\nclass\n,", "heading": "SERVICE_URL", "level": 2, "chunk_index": 159, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_160", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SERVICE_URL\n\n,\n10000\n);\nString\nsessionToken\n=\nas\n.\nlogin\n(\n\"admin\"\n,\n\"password\"\n);\n// Download a single file with a path and a dataset code\nDataSetFileDownloadOptions\noptions\n=\nnew\nDataSetFileDownloadOptions\n();\noptions\n.\nsetRecursive\n(\nfalse\n);\nIDataSetFileId\nfileToDownload\n=\nnew\nDataSetFilePermId\n(\nnew\nDataSetPermId\n(\n\"20161205154857065-25\"\n),\n\"root/subfolder/file3.txt\"\n);\n// Download the files into a stream and read them with the file reader\n// Here there is only one file, but we need to put it in an array anyway\nInputStream\nstream\n=\ndss\n.\ndownloadFiles\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nfileToDownload\n),\noptions\n);\nDataSetFileDownloadReader\nreader\n=\nnew\nDataSetFileDownloadReader\n(\nstream\n);\nDataSetFileDownload\nfile\n=\nnull\n;\n// Print out the contents\nwhile\n((\nfile\n=\nreader\n.\nread\n())\n!=\nnull\n)\n{\nSystem\n.\nout\n.\nprintln\n(\n\"Downloaded \"\n+\nfile\n.\ngetDataSetFile\n().\ngetPath\n()\n+\n\" \"\n+\nfile\n.\ngetDataSetFile\n().\ngetFileLength\n());\nSystem\n.\nout\n.\nprintln\n(", "heading": "SERVICE_URL", "level": 2, "chunk_index": 160, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_161", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"-----FILE CONTENTS-----\"\n\n);\nSystem\n.\nout\n.\nprintln\n(\nfile\n.\ngetInputStream\n());\n}\n}\n}\nDownload a folder located inside a dataset\n\nThe example below demonstrates how to download a folder and all its\ncontents, when the dataset code and the folder path are known. The goal\nhere is to download the directory called “subfolder” and the file\n“file3.txt” which will return two objects, one representing the metadata\nof the directory, and the other representing both the meta data of\nfile3.txt and the file contents. Note that setting recursive flag to\ntrue will return both the subfolder directory object AND file3.txt,\nwhile setting recursive flag to false will return just the meta data of\nthe directory object.\nDownload a folder\nimport\njava.io.InputStream\n;\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.id.DataSetPermId\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.IDataStoreServerApi\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownload\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownloadOptions\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownloadReader\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.id.DataSetFilePermId\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.id.IDataSetFileId\n;\nimport\nch.systemsx.cisd.common.spring.HttpInvokerUtils\n;\npublic\nclass\nV3DSSExample2\n{", "heading": "\"-----FILE CONTENTS-----\"", "level": 2, "chunk_index": 161, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_162", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "// DATASET EXAMPLE STRUCTURE\n\n// The dataset consists of a root folder with 2 files and a subfolder with 1 file", "heading": "// DATASET EXAMPLE STRUCTURE", "level": 2, "chunk_index": 162, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_163", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "DSS_URL\n\n=\n\"https://localhost:8444/datastore_server\"\n;\n// Reference the DSS\nIDataStoreServerApi\ndss\n=\nHttpInvokerUtils\n.\ncreateStreamSupportingServiceStub\n(\nIDataStoreServerApi\n.\nclass\n,", "heading": "DSS_URL", "level": 2, "chunk_index": 163, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_164", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SERVICE_URL\n\n,\n10000\n);\n// Reference the AS and login & get a session token\nIApplicationServerApi\nas\n=\nHttpInvokerUtils\n.\ncreateServiceStub\n(\nIApplicationServerApi\n.\nclass\n,", "heading": "SERVICE_URL", "level": 2, "chunk_index": 164, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_165", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SERVICE_URL\n\n,\n10000\n);\nString\nsessionToken\n=\nas\n.\nlogin\n(\n\"admin\"\n,\n\"password\"\n);\n// Download a single folder (containing a file inside) with a path and a data set code\nDataSetFileDownloadOptions\noptions\n=\nnew\nDataSetFileDownloadOptions\n();\nIDataSetFileId\nfileToDownload\n=\nnew\nDataSetFilePermId\n(\nnew\nDataSetPermId\n(\n\"20161205154857065-25\"\n),\n\"root/subfolder\"\n);\n// Setting recursive flag to true will return both the subfolder directory object AND file3.txt\noptions\n.\nsetRecursive\n(\ntrue\n);\n// Setting recursive flag to false will return just the meta data of the directory object\n//options.setRecursive(false);\n// Read the contents and print them out\nInputStream\nstream\n=\ndss\n.\ndownloadFiles\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nfileToDownload\n),\noptions\n);\nDataSetFileDownloadReader\nreader\n=\nnew\nDataSetFileDownloadReader\n(\nstream\n);\nDataSetFileDownload\nfile\n=\nnull\n;\nwhile\n((\nfile\n=\nreader\n.\nread\n())\n!=\nnull\n)\n{\nSystem\n.\nout\n.\nprintln\n(\n\"Downloaded \"\n+\nfile\n.\ngetDataSetFile\n().\ngetPath\n()\n+\n\" \"\n+\nfile\n.\ngetDataSetFile\n().\ngetFileLength\n());\nSystem\n.\nout\n.\nprintln\n(", "heading": "SERVICE_URL", "level": 2, "chunk_index": 165, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_166", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "\"-----FILE CONTENTS-----\"\n\n);\nSystem\n.\nout\n.\nprintln\n(\nfile\n.\ngetInputStream\n());\n}\n}\n}\nSearch for a dataset and download all its contents, file by file\n\nHere is an example that demonstrates how to search for datasets and\ndownload the contents file by file. Here recursion is not used - see\nexample 4 for a recursive example. To search for datasets, it is\nnecessary to assign the appropriate criteria in the\nDataSetFileSearchCriteria object. It is also possible to search for\ndatasets that contain certain files, as demonstrated below. Searching\nfor files via the searchFiles method returns a list of DataSetFile\nobjects that contain meta data about the files and also the file\ncontents. The meta data includes the file perm ids, the dataset perm ids\n(the perm ids are objects, not simple codes!), the file path, the file\nlength, and whether or not the file is a directory. With this list of\nfiles, it is possible to iterate and access the contents as shown in\nthis example.\nSearch & download a whole dataset, file by file\nimport\njava.io.InputStream\n;\nimport\njava.util.LinkedList\n;\nimport\njava.util.List\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.IDataStoreServerApi\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.DataSetFile\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownload\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownloadOptions\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownloadReader\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.fetchoptions.DataSetFileFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.id.IDataSetFileId\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.search.DataSetFileSearchCriteria\n;\nimport\nch.systemsx.cisd.common.spring.HttpInvokerUtils\n;\npublic\nclass\nV3DSSExample3\n{", "heading": "\"-----FILE CONTENTS-----\"", "level": 2, "chunk_index": 166, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_167", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "// DATASET EXAMPLE STRUCTURE\n\n// The dataset consists of a root folder with 2 files and a subfolder with 1 file", "heading": "// DATASET EXAMPLE STRUCTURE", "level": 2, "chunk_index": 167, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_168", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "DSS_URL\n\n=\n\"https://localhost:8444/datastore_server\"\n;\n// Reference the DSS\nIDataStoreServerApi\ndss\n=\nHttpInvokerUtils\n.\ncreateStreamSupportingServiceStub\n(\nIDataStoreServerApi\n.\nclass\n,", "heading": "DSS_URL", "level": 2, "chunk_index": 168, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_169", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SERVICE_URL\n\n,\n10000\n);\n// Reference the AS and login & get a session token\nIApplicationServerApi\nas\n=\nHttpInvokerUtils\n.\ncreateServiceStub\n(\nIApplicationServerApi\n.\nclass\n,", "heading": "SERVICE_URL", "level": 2, "chunk_index": 169, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_170", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SERVICE_URL\n\n,\n10000\n);\nString\nsessionToken\n=\nas\n.\nlogin\n(\n\"admin\"\n,\n\"password\"\n);\n// Create search criteria\nDataSetFileSearchCriteria\ncriteria\n=\nnew\nDataSetFileSearchCriteria\n();\ncriteria\n.\nwithDataSet\n().\nwithCode\n().\nthatEquals\n(\n\"20161205154857065-25\"\n);\n// Search for a dataset with a certain file inside like this:\n//criteria.withDataSet().withChildren().withPermId(mypermid);\n// Search for the files & put the file perm ids in a list for easy access\n// (file perm ids are objects containing meta data describing the file)\nSearchResult\n<\nDataSetFile\n>\nresult\n=\ndss\n.\nsearchFiles\n(\nsessionToken\n,\ncriteria\n,\nnew\nDataSetFileFetchOptions\n());\nList\n<\nDataSetFile\n>\nfiles\n=\nresult\n.\ngetObjects\n();", "heading": "SERVICE_URL", "level": 2, "chunk_index": 170, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_171", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "// This returns the following list of objects:\n\n// DataSetFile(\"root\", isDirectory = true)\n// DataSetFile(\"root/file1.txt\", isDirectory = false)\n// DataSetFile(\"root/file2.txt\", isDirectory = false)\n// DataSetFile(\"root/subfolder\", isDirectory = true)\n// DataSetFile(\"root/subfolder/file3.txt\", isDirectory = false)\nList\n<\nIDataSetFileId\n>\nfileIds\n=\nnew\nLinkedList\n<\nIDataSetFileId\n>\n();\nfor\n(\nDataSetFile\nfile", "heading": "// This returns the following list of objects:", "level": 3, "chunk_index": 171, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_172", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nfiles\n)\n{\nSystem\n.\nout\n.\nprintln\n(\nfile\n.\ngetPath\n()\n+\n\" \"\n+\nfile\n.\ngetFileLength\n());\nfileIds\n.\nadd\n(\nfile\n.\ngetPermId\n());\n}\n// Download the files & print the contents\nDataSetFileDownloadOptions\noptions\n=\nnew\nDataSetFileDownloadOptions\n();\noptions\n.\nsetRecursive\n(\nfalse\n);\nInputStream\nstream\n=\ndss\n.\ndownloadFiles\n(\nsessionToken\n,\nfileIds\n,\noptions\n);\nDataSetFileDownloadReader\nreader\n=\nnew\nDataSetFileDownloadReader\n(\nstream\n);\nDataSetFileDownload\nfile\n=\nnull\n;\nwhile\n((\nfile\n=\nreader\n.\nread\n())\n!=\nnull\n)\n{\nSystem\n.\nout\n.\nprintln\n(\n\"Downloaded \"\n+\nfile\n.\ngetDataSetFile\n().\ngetPath\n()\n+\n\" \"\n+\nfile\n.\ngetDataSetFile\n().\ngetFileLength\n());\nSystem\n.\nout\n.\nprintln\n(\nfile\n.\ngetInputStream\n());\n}\n}\n}\nDownload a whole dataset recursively\n\nHere is a simplified way to download a dataset. Instead of downloading\nfiles one by one, it is possible to download the entire dataset\nrecursively by simply setting the recursive file to true in the\nDataSetFileDownloadOptions object.\nDownload a whole dataset recursively\nimport\njava.io.InputStream\n;\nimport\njava.util.Arrays\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.id.DataSetPermId\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.IDataStoreServerApi\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownload\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownloadOptions\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownloadReader\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.id.DataSetFilePermId\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.id.IDataSetFileId\n;\nimport\nch.systemsx.cisd.common.spring.HttpInvokerUtils\n;\npublic\nclass\nV3DSSExample4\n{", "heading": ":", "level": 3, "chunk_index": 172, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_173", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "// DATASET EXAMPLE STRUCTURE\n\n// The dataset consists of a root folder with 2 files and a subfolder with 1 file", "heading": "// DATASET EXAMPLE STRUCTURE", "level": 2, "chunk_index": 173, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_174", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "DSS_URL\n\n=\n\"https://localhost:8444/datastore_server\"\n;\n// Reference the DSS\nIDataStoreServerApi\ndss\n=\nHttpInvokerUtils\n.\ncreateStreamSupportingServiceStub\n(\nIDataStoreServerApi\n.\nclass\n,", "heading": "DSS_URL", "level": 2, "chunk_index": 174, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_175", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SERVICE_URL\n\n,\n10000\n);\n// Reference the AS and login & get a session token\nIApplicationServerApi\nas\n=\nHttpInvokerUtils\n.\ncreateServiceStub\n(\nIApplicationServerApi\n.\nclass\n,", "heading": "SERVICE_URL", "level": 2, "chunk_index": 175, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_176", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SERVICE_URL\n\n,\n10000\n);\nString\nsessionToken\n=\nas\n.\nlogin\n(\n\"admin\"\n,\n\"password\"\n);\n// Download the files and print the contents\nDataSetFileDownloadOptions\noptions\n=\nnew\nDataSetFileDownloadOptions\n();\nIDataSetFileId\nfileId\n=\nnew\nDataSetFilePermId\n(\nnew\nDataSetPermId\n(\n\"20161205154857065-25\"\n));\noptions\n.\nsetRecursive\n(\ntrue\n);\nInputStream\nstream\n=\ndss\n.\ndownloadFiles\n(\nsessionToken\n,\nArrays\n.\nasList\n(\nfileId\n),\noptions\n);\nDataSetFileDownloadReader\nreader\n=\nnew\nDataSetFileDownloadReader\n(\nstream\n);\nDataSetFileDownload\nfile\n=\nnull\n;\nwhile\n((\nfile\n=\nreader\n.\nread\n())\n!=\nnull\n)\n{\nfile\n.\ngetInputStream\n();\nSystem\n.\nout\n.\nprintln\n(\n\"Downloaded \"\n+\nfile\n.\ngetDataSetFile\n().\ngetPath\n()\n+\n\" \"\n+\nfile\n.\ngetDataSetFile\n().\ngetFileLength\n());\n}\n}\n}\nSearch and list all the files inside a data store\n\nHere is an example that demonstrates how to list all the files in a data\nstore. By simply leaving the following line as is:\nDataSetFileSearchCriteria criteria = new DataSetFileSearchCriteria();\nit will automatically return every object in the data store. This is\nuseful when it is desired to list an entire directory or iterate over\nthe whole data store.\nSearch and list all files inside a data store\nimport\njava.io.InputStream\n;\nimport\njava.util.LinkedList\n;\nimport\njava.util.List\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.common.search.SearchResult\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.IDataStoreServerApi\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.DataSetFile\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownload\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownloadOptions\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.download.DataSetFileDownloadReader\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.fetchoptions.DataSetFileFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.id.IDataSetFileId\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.search.DataSetFileSearchCriteria\n;\nimport\nch.systemsx.cisd.common.spring.HttpInvokerUtils\n;\npublic\nclass\nV3DSSExample5\n{", "heading": "SERVICE_URL", "level": 2, "chunk_index": 176, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_177", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "// DATASET EXAMPLE STRUCTURE\n\n// The dataset consists of a root folder with 2 files and a subfolder with 1 file", "heading": "// DATASET EXAMPLE STRUCTURE", "level": 2, "chunk_index": 177, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_178", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "DSS_URL\n\n=\n\"https://localhost:8444/datastore_server\"\n;\n// Reference the DSS\nIDataStoreServerApi\ndss\n=\nHttpInvokerUtils\n.\ncreateStreamSupportingServiceStub\n(\nIDataStoreServerApi\n.\nclass\n,", "heading": "DSS_URL", "level": 2, "chunk_index": 178, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_179", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SERVICE_URL\n\n,\n10000\n);\n// Reference the AS and login & get a session token\nIApplicationServerApi\nas\n=\nHttpInvokerUtils\n.\ncreateServiceStub\n(\nIApplicationServerApi\n.\nclass\n,", "heading": "SERVICE_URL", "level": 2, "chunk_index": 179, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_180", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SERVICE_URL\n\n,\n10000\n);\nString\nsessionToken\n=\nas\n.\nlogin\n(\n\"admin\"\n,\n\"password\"\n);\n// Create search criteria\nDataSetFileSearchCriteria\ncriteria\n=\nnew\nDataSetFileSearchCriteria\n();\ncriteria\n.\nwithDataSet\n();\n//comment out this line below, and just leave the criteria empty - and it will return everything.\n//criteria.withDataSet().withCode().thatEquals(\"20151201115639682-98322\");\n// Search for the files & put the file perm ids (objects containing meta data) in a list for easy access\nSearchResult\n<\nDataSetFile\n>\nresult\n=\ndss\n.\nsearchFiles\n(\nsessionToken\n,\ncriteria\n,\nnew\nDataSetFileFetchOptions\n());\nList\n<\nDataSetFile\n>\nfiles\n=\nresult\n.\ngetObjects\n();\nList\n<\nIDataSetFileId\n>\nfileIds\n=\nnew\nLinkedList\n<\nIDataSetFileId\n>\n();\nfor\n(\nDataSetFile\nfile", "heading": "SERVICE_URL", "level": 2, "chunk_index": 180, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_181", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nfiles\n)\n{\nSystem\n.\nout\n.\nprintln\n(\nfile\n.\ngetPath\n()\n+\n\" \"\n+\nfile\n.\ngetFileLength\n());\nfileIds\n.\nadd\n(\nfile\n.\ngetPermId\n());\n}\n// Download the files and print the contents\nDataSetFileDownloadOptions\noptions\n=\nnew\nDataSetFileDownloadOptions\n();\noptions\n.\nsetRecursive\n(\nfalse\n);\nInputStream\nstream\n=\ndss\n.\ndownloadFiles\n(\nsessionToken\n,\nfileIds\n,\noptions\n);\nDataSetFileDownloadReader\nreader\n=\nnew\nDataSetFileDownloadReader\n(\nstream\n);\nDataSetFileDownload\nfile\n=\nnull\n;\nwhile\n((\nfile\n=\nreader\n.\nread\n())\n!=\nnull\n)\n{\nSystem\n.\nout\n.\nprintln\n(\n\"Downloaded \"\n+\nfile\n.\ngetDataSetFile\n().\ngetPath\n()\n+\n\" \"\n+\nfile\n.\ngetDataSetFile\n().\ngetFileLength\n());\nSystem\n.\nout\n.\nprintln\n(", "heading": ":", "level": 3, "chunk_index": 181, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_182", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "library. Downloading is done in two steps:\n\nCreate a fast download session with the\nmethod\ncreateFastDownloadSession()\non  V3 DSS API. One parameter\nis a list of data set file ids. Such an id contains the data set\ncode and the path to the file inside the data set. If a file id\npoints to a folder the whole folder will be downloaded. The last\nparameter specifies download preferences. Currently only the wished\nnumber of parallel download streams can be specified. The API call\nreturns a\nFastDownloadSession\nobject.\nDownload the files with the helper class\nFastDownloader\n. The\nsimplest usage is just do ``\nSearch and list all files inside a data store\nnew FastDownloader(downloadSession).downloadTo(destinationFolder);\nThe files are stored in the destination folder in\n/\n.", "heading": "library. Downloading is done in two steps:", "level": 3, "chunk_index": 182, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_183", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Here is a complete example:\n\nSearch and list all files inside a data store\nimport\njava.io.File\n;\nimport\njava.nio.file.Path\n;\nimport\njava.util.ArrayList\n;\nimport\njava.util.Collection\n;\nimport\njava.util.List\n;\nimport\njava.util.Map\n;\nimport\njava.util.Map.Entry\n;\nimport\norg.apache.commons.lang3.time.StopWatch\n;\nimport\nch.ethz.sis.filetransfer.DownloadListenerAdapter\n;\nimport\nch.ethz.sis.filetransfer.IDownloadItemId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.DataSet\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.fetchoptions.DataSetFetchOptions\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.id.DataSetPermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.search.DataSetSearchCriteria\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.IDataStoreServerApi\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.fastdownload.FastDownloadSession\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.fastdownload.FastDownloadSessionOptions\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.id.DataSetFilePermId\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.datasetfile.id.IDataSetFileId\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.fastdownload.FastDownloadResult\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.fastdownload.FastDownloader\n;\nimport\nch.systemsx.cisd.common.spring.HttpInvokerUtils\n;\nimport\nch.systemsx.cisd.openbis.common.api.client.ServiceFinder\n;\npublic\nclass\nV3FastDownloadExample\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\n{\nIApplicationServerApi\nv3\n=\nHttpInvokerUtils\n.\ncreateServiceStub\n(\nIApplicationServerApi\n.\nclass\n,\n\"http://localhost:8888/openbis/openbis\"\n+\nIApplicationServerApi\n.", "heading": "Here is a complete example:", "level": 3, "chunk_index": 183, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_184", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SERVICE_URL\n\n,\n10000\n);\nString\nsessionToken\n=\nv3\n.\nlogin\n(\n\"test\"\n,\n\"password\"\n);\n// Search for some data sets\nDataSetSearchCriteria\nsearchCriteria\n=\nnew\nDataSetSearchCriteria\n();\nsearchCriteria\n.\nwithCode\n().\nthatStartsWith\n(\n\"201902\"\n);\nDataSetFetchOptions\nfetchOptions\n=\nnew\nDataSetFetchOptions\n();\nfetchOptions\n.\nwithDataStore\n();\nfetchOptions\n.\nwithPhysicalData\n();\nList\n<\nDataSet\n>\ndataSets\n=\nv3\n.\nsearchDataSets\n(\nsessionToken\n,\nsearchCriteria\n,\nfetchOptions\n).\ngetObjects\n();\n// Get the DSS URL from the first data set assuming that all data sets from the same data store\nString\ndssUrl\n=\ndataSets\n.\nget\n(\n0\n).\ngetDataStore\n().\ngetDownloadUrl\n();\nSystem\n.\nout\n.\nprintln\n(\n\"url:\"\n+\ndssUrl\n);\n// Create DSS server\nIDataStoreServerApi\ndssServer\n=\nnew\nServiceFinder\n(\n\"datastore_server\"\n,\nIDataStoreServerApi\n.", "heading": "SERVICE_URL", "level": 2, "chunk_index": 184, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_185", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "SERVICE_URL\n\n)\n.\ncreateService\n(\nIDataStoreServerApi\n.\nclass\n,\ndssUrl\n);\n// We download all files of the all found data sets.\nList\n<\nDataSetFilePermId\n>\nfileIds\n=\nnew\nArrayList\n<>\n();\nfor\n(\nDataSet\ndataSet", "heading": "SERVICE_URL", "level": 2, "chunk_index": 185, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_186", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\ndataSets\n)\n{\nfileIds\n.\nadd\n(\nnew\nDataSetFilePermId\n(\nnew\nDataSetPermId\n(\ndataSet\n.\ngetCode\n())));\n}\n// Create the download session for 2 streams in parallel (if possible)\nFastDownloadSession\ndownloadSession\n=\ndssServer\n.\ncreateFastDownloadSession\n(\nsessionToken\n,\nfileIds\n,\nnew\nFastDownloadSessionOptions\n().\nwithWishedNumberOfStreams\n(\n2\n));\n// Do the actual download into 'targets/fast-download' and print the time needed by using a download listener\nFastDownloadResult\nresult\n=\nnew\nFastDownloader\n(\ndownloadSession\n).\nwithListener\n(\nnew\nDownloadListenerAdapter\n()\n{\nprivate\nStopWatch\nstopWatch\n=\nnew\nStopWatch\n();\n@Override\npublic\nvoid\nonDownloadStarted\n()\n{\nstopWatch\n.\nstart\n();\n}\n@Override\npublic\nvoid\nonDownloadFinished\n(\nMap\n<\nIDownloadItemId\n,\nPath\n>\nitemPaths\n)\n{\nSystem\n.\nout\n.\nprintln\n(\n\"Successfully finished after \"\n+\nstopWatch\n);\n}\n@Override\npublic\nvoid\nonDownloadFailed\n(\nCollection\n<\nException\n>\ne\n)\n{\nSystem\n.\nout\n.\nprintln\n(\n\"Downloading failed after \"\n+\nstopWatch\n);\n}\n})\n.\ndownloadTo\n(\nnew\nFile\n(\n\"targets/fast-download\"\n));\n// Print the mapping of data set file id to the actual path\nfor\n(\nEntry\n<\nIDataSetFileId\n,\nPath\n>\nentry", "heading": ":", "level": 3, "chunk_index": 186, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_187", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": ":\n\nresult\n.\ngetPathsById\n().\nentrySet\n())\n{\nSystem\n.\nout\n.\nprintln\n(\nentry\n);\n}\nv3\n.\nlogout\n(\nsessionToken\n);\n}\n}\nWhat happens under the hood?\n\nThe files to be downloaded are chunked into chunks of maximum size 1 MB.\nOn the DSS a special web service (\nFileTransferServerServlet\n) provides\nthese chunks. On the client side these chunks are requested and stored\nin the file system. This is done in parallel if possible and requested\n(withWishedNumberOfStreams). The server tells the client the actual\nnumber of streams available for parallel downloading without slowing\ndown DSS. The actual number of streams depends on\nthe wished number of streams\nthe number of streams currently used by other download sessions\nthe maximum number of allowed streams as specified by the\nproperty\napi.v3.fast-download.maximum-number-of-allowed-streams\nin\nDSS\nservice.properties\n. Default value is 10.\nThe actual number of streams is half of the number of free streams or\nthe wished number of streams, if it is less. The number of free streams\nis given by the difference between the maximum number of allowed streams\nand the total number of used streams.\nIt is possible that the actual number of streams is zero if the server\nis currently too busy with downloading (that is, there is no free\ndowload stream available). The FastDownloader will retry it later.", "heading": ":", "level": 3, "chunk_index": 187, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_188", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "There are three ways to customizing the FastDownloader:\n\nwithListener(): Adds a listener which will be notified when\nthe download session has been started/finished/failed,\nthe download of a file/folder has been started/finished and\na chunk has been downloaded.\nThere can be several listeners. By default there are no\nlisteners. Note, that listeners are notified in a separated\nthread associated with the download session.\nwithLogger(): Sets a logger. By default nothing is logged.\nwithRetryProviderFactory(): Sets the factory which creates a retry\nprovider. A retry provider knows when and how often a failed action\n(e.g. sever request) should be retried. By default it is retried\nthree times. The first retry is a second later. For each following\nretry the waiting time is increases by the factor two.", "heading": "There are three ways to customizing the FastDownloader:", "level": 3, "chunk_index": 188, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_189", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Register Data Sets\n\n\nTo register datasets using the Java or JavaScript API use one of the\nfollowing examples as a template.\nExample (Java)", "heading": "Register Data Sets", "level": 3, "chunk_index": 189, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_190", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Register Data Set\n\nimport\njava.util.UUID\n;\nimport\norg.eclipse.jetty.client.HttpClient\n;\nimport\norg.eclipse.jetty.client.api.Request\n;\nimport\norg.eclipse.jetty.client.util.MultiPartContentProvider\n;\nimport\norg.eclipse.jetty.client.util.StringContentProvider\n;\nimport\norg.eclipse.jetty.http.HttpMethod\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.IApplicationServerApi\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.dataset.id.DataSetPermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.entitytype.id.EntityTypePermId\n;\nimport\nch.ethz.sis.openbis.generic.asapi.v3.dto.sample.id.SampleIdentifier\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.IDataStoreServerApi\n;\nimport\nch.ethz.sis.openbis.generic.dssapi.v3.dto.dataset.create.UploadedDataSetCreation\n;\nimport\nch.systemsx.cisd.common.http.JettyHttpClientFactory\n;\nimport\nch.systemsx.cisd.common.spring.HttpInvokerUtils\n;\npublic\nclass\nRegisterDataSet\n{\npublic\nstatic\nvoid\nmain\n(\nString\n[]\nargs\n)\nthrows\nException\n{\nfinal\nString", "heading": "Register Data Set", "level": 3, "chunk_index": 190, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_191", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "DSS_URL\n\n);\nopenbisV3\n.\nlogin\n(\n\"admin\"\n,\n\"password\"\n);\nfinal\nPath\npath\n=\nPath\n.\nof\n(\n\"/uploadPath\"\n);\nfinal\nString\nuploadId\n=\nopenbisV3\n.\nuploadFileWorkspaceDSS\n(\npath\n);\nfinal\nUploadedDataSetCreation\ncreation\n=\nnew\nUploadedDataSetCreation\n();\ncreation\n.\nsetUploadId\n(\nuploadId\n);\ncreation\n.\nsetExperimentId\n(\nnew\nExperimentIdentifier\n(", "heading": "DSS_URL", "level": 2, "chunk_index": 191, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_192", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "DATA_SET\n\n));\ntry\n{\nfinal\nDataSetPermId\ndataSetPermId\n=\nopenbisV3\n.\ncreateUploadedDataSet\n(\ncreation\n);\n// A data set assigned to the experiment \"/DEFAULT/DEFAULT/DEFAULT\" with the folder \"uploadPath\" is created\nSystem\n.\nout\n.\nprintln\n(\n\"dataSetPermId=\"\n+\ndataSetPermId\n);\n}\ncatch\n(\nfinal\nException\ne\n)\n{\ne\n.\nprintStackTrace\n();\n}\nopenbisV3\n.\nlogout\n();\n}\n}\nExample (Javascript)", "heading": "DATA_SET", "level": 2, "chunk_index": 192, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_193", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Register Data Set\n\n<!DOCTYPE html>\n<\nhtml\n>\n<\nhead\n>\n<\nmeta\ncharset\n=\n\"utf-8\"\n>\n<\ntitle\n>\nDataset upload\n</\ntitle\n>\n<\nscript\ntype\n=\n\"text/javascript\"\nsrc\n=\n\"/openbis-test/resources/api/v3/config.js\"\n></\nscript\n>\n<\nscript\ntype\n=\n\"text/javascript\"\nsrc\n=\n\"/openbis-test/resources/api/v3/require.js\"\n></\nscript\n>\n</\nhead\n>\n<\nbody\n>\n<\nlabel\nfor\n=\n\"myfile\"\n>", "heading": "Register Data Set", "level": 3, "chunk_index": 193, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_194", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "Select a file:\n\n</\nlabel\n>\n<\ninput\ntype\n=\n\"file\"\nid\n=\n\"myFile\"\n/>\n<\nscript\n>\nrequire\n([\n\"openbis\"\n,\n\"dss/dto/dataset/create/UploadedDataSetCreation\"\n,\n\"as/dto/experiment/id/ExperimentIdentifier\"\n,\n\"as/dto/entitytype/id/EntityTypePermId\"\n,\n\"as/dto/entitytype/EntityKind\"\n],\nfunction\n(\nopenbis\n,\nUploadedDataSetCreation\n,\nExperimentIdentifier\n,\nEntityTypePermId\n,\nEntityKind\n)\n{\nvar\ntestProtocol\n=\nwindow\n.\nlocation\n.\nprotocol\n;\nvar\ntestHost\n=\nwindow\n.\nlocation\n.\nhostname\n;\nvar\ntestPort\n=\nwindow\n.\nlocation\n.\nport\n;\nvar\ntestUrl\n=\ntestProtocol\n+\n\"//\"\n+\ntestHost\n+\n\":\"\n+\ntestPort\n;\nvar\ntestApiUrl\n=\ntestUrl\n+\n\"/openbis/openbis/rmi-application-server-v3.json\"\n;\nvar\nopenbisV3\n=\nnew\nopenbis\n(\ntestApiUrl\n);\nvar\nfileInput\n=\ndocument\n.\ngetElementById\n(\n\"myFile\"\n);\nfileInput\n.\nonchange\n=\n(\ne\n)\n=>\n{\nvar\nfiles\n=\ne\n.\ntarget\n.\nfiles\n;\nopenbisV3\n.\nlogin\n(\n\"admin\"\n,\n\"password\"\n).\ndone\n(\nsessionToken\n=>\n{\nvar\ndataStoreFacade\n=\nopenbisV3\n.\ngetDataStoreFacade\n();\ndataStoreFacade\n.\nuploadFilesWorkspaceDSS\n(\nfiles\n).\ndone\n(\nuploadId\n=>\n{\nvar\ncreation\n=\nnew\nUploadedDataSetCreation\n();\ncreation\n.\nsetUploadId\n(\nuploadId\n);\ncreation\n.\nsetExperimentId\n(\nnew\nExperimentIdentifier\n(", "heading": "Select a file:", "level": 3, "chunk_index": 194, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api_195", "title": "Java / Javascript (V3 API) - openBIS V3 API", "url": "https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/apis/java-javascript-v3-api.html", "source": "openbis", "text": "DATA_SET\n\n));\ndataStoreFacade\n.\ncreateUploadedDataSet\n(\ncreation\n).\ndone\n(\ndataSetPermId\n=>\n{\n// A data set assigned to the experiment \"/DEFAULT/DEFAULT/DEFAULT\" with the folder \"uploadPath\" is created\nconsole\n.\nlog\n(\n\"dataSetPermId=\"\n+\ndataSetPermId\n);\nopenbisV3\n.\nlogout\n();\n}).\nfail\n(\nerror\n=>\n{\nconsole\n.\nerror\n(\nerror\n);\nopenbisV3\n.\nlogout\n();\n});\n});\n});\n}\n});\n</\nscript\n>\n</\nbody\n>\n</\nhtml\n>\nVI. Web application context\n\nWhen making web applications and embedding them into an openBIS tab on\nthe core UI is often required to have information about the context\nthose applications are being loaded for two particular purposes:\nMaking the application context sensitive and show\ninformation/functionality related to the current context. The\ncontext object provided by\ngetWebAppContext()\ncontains all\ninformation required for this purpose.\nLogin into the facade without presenting the user with another login\nscreen since they have already login into openBIS. For\nthat\nloginFromContext()\ncan be used.\nThis methods only exist on the Javascript facade with the purpose of\nbeing used on embedded web applications, calling them from an external\nweb application will do nothing.\nWebAppContextExample.html\n<\nscript\n>\nrequire\n([\n'openbis'\n],\nfunction\n(\nopenbis\n)\n{\nvar\nopenbisV3\n=\nnew\nopenbis\n();\nvar\nwebappcontext\n=\nopenbisV3\n.\ngetWebAppContext\n();\nconsole\n.\nlog\n(\nwebappcontext\n.\ngetWebappCode\n());\nconsole\n.\nlog\n(\nwebappcontext\n.\ngetSessionId\n());\nconsole\n.\nlog\n(\nwebappcontext\n.\ngetEntityKind\n());\nconsole\n.\nlog\n(\nwebappcontext\n.\ngetEntityType\n());\nconsole\n.\nlog\n(\nwebappcontext\n.\ngetEntityIdentifier\n());\nconsole\n.\nlog\n(\nwebappcontext\n.\ngetEntityPermId\n());\nopenbisV3\n.\nloginFromContext\n();\nopenbisV3\n.\ngetSessionInformation\n().\ndone\n(\nfunction\n(\nsessionInfo\n)\n{\nconsole\n.\nlog\n(\nsessionInfo\n.\ngetUserName\n());\n});\n});\n</\nscript\n>", "heading": "DATA_SET", "level": 2, "chunk_index": 195, "file_path": "openbis\\en_20.10.0-11_software-developer-documentation_apis_java-javascript-v3-api.txt", "timestamp": "2025-09-16T17:51:18.997351"}, {"id": "en_concepts_0", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Last Friday at 11:54 AM\n\n¶\nCollection\nIn openBIS, a\nCollection\nis a folder with user-defined\nProperties\nlocated on the third level of the hierarchical data structure (Space/Project/\nCollection\n/Object). A\nCollection\nis always part of a\nProject\n.\nCollections\nof the same type are described by the same set of\nProperties\n.\nCollection\ntypes are defined as part of the openBIS\nmasterdata\n.\nDatasets\ncan be attached to\nCollections\n.\nA\nCollection\ncan logically group an unlimited number of\nObject\nof one or more\nObject\ntypes. For instance, a\nCollection\nof the type \"Measurement Devices\" can be used to organize\nObjects\nof the type \"Instrument\" in the\nInventory\n. A\nCollection\nof the type \"Default Experiment\" can be used to organize\nObjects\nof the type \"Experimental Step\" in the", "heading": "Last Friday at 11:54 AM", "level": 3, "chunk_index": 0, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.003163"}, {"id": "en_concepts_1", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Controlled Vocabulary\n\nA controlled vocabulary is an established list of terms to provide consistency and uniqueness in the description of a given domain, e.g., a list of room labels, SI units or purity grades. In openBIS, controlled vocabularies are a possible data type for metadata\nProperties\n. Each term in a controlled vocabulary has a code, a label, and a description. All existing controlled vocabularies and their terms are listed in the Vocabulary Browser in the Utilities.\n¶\nDataset\nIn openBIS, a\nDataset\nis a folder with user-defined\nProperties\nthat can contain individual files of arbitrary formats (e.g., images, csv files, xml files, etc.) as well as complex folder structures with subfolders and many files (of potentially different formats). The content of\nDatasets\n(but not their metadata) is immutable, i.e., it cannot be edited after creation.\nDatasets\nof the same type are described by the same set of\nProperties\n.\nDataset\ntypes are defined as part of the openBIS\nmasterdata\n.\nA\nDataset\nhas to be attached to either an\nObject\nor to a\nCollection\n. Different\nDatasets\ncan be connected via\nparent-child relationships\n.\n¶", "heading": "Controlled Vocabulary", "level": 3, "chunk_index": 1, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.003163"}, {"id": "en_concepts_2", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Data Structure\n\nThe openBIS data structure is hierarchically organized in five (sub)folders called\nSpace\n,\nProject\n,\nCollection\n,\nObject\nand\nDataset\n. Folder names can be customized by users.\nTo digitally represent an\nObject\n, a\nSpace\n,\nProject\nand\nCollection\nneed to be created first.\n¶\nDropbox\nThe Dropbox is a core openBIS plugin that allows the upload of (large) data files to an openBIS instance. Instead of using the user interface for\nDataset\nregistration, users move their data files (+ information about the\nObject\nor\nCollection\nthey should be attached to) to a dedicated Dropbox folder in a file-service at BAM that is continously monitored. Once new data files are detected inside the Dropbox folder, they are automatically uploaded as\nDatasets\nto the openBIS instance.\nIt is possible to control the\nDataset\nregistration process via Dropbox scripts written in Python. The script can register new\nDatasets\n,\nObjects\n,\nProperties\nand\nparent-child relationships\nas part of its processing. The Dropbox framework also provides tools to track file operations and, if necessary, revert them, ensuring that the incoming file or directory is returned to its original state in the event of an error.\nThe Dropbox is not related to the commercial file hosting service.\n¶", "heading": "Data Structure", "level": 3, "chunk_index": 2, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.003163"}, {"id": "en_concepts_3", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Entity and Entity Types\n\nAn Entity is an item of the \"real world\" (tangible/non tangible) that is uniquely identified by attributes (\nProperties\n). An Entity Type is a collection of Entities with similar properties. An Entity Type is an object in a data model. In openBIS relevant entity types are\nCollection\n,\nObject\nand\nDataset\ntypes. Entity types can only be created by someone with the Instance admin role.\n¶\nInventory\nThe Inventory is one of the two main components of openBIS. It is used for the digital representation of shared laboratory inventory and the storage of related data files such as measuring instruments, chemical substances, and samples, but can also be user for storing protocols, standard operating procedures (SOPs) and publications. The Inventory is organized into\nSpaces\n,\nProjects\nand\nCollections\naccording to the hierarchical\ndata structure\nof openBIS.\nBy default, each BAM division gets\nprivate\nInventory\nSpaces\n(Equipment, Materials, Methods, Publications) that are only accessible to division members\npublic\nInventory\nProjects\n(Equipment, Materials, Methods) that are accessible to every user of the Data Store.\n¶", "heading": "Entity and Entity Types", "level": 3, "chunk_index": 3, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.003163"}, {"id": "en_concepts_4", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Intances in the Data Store\n\nIn the Data Store, various instances are available to support users during different stages of the onboarding and data management process. Below are two key instances and their roles:\nInstance\nUsers\nConfiguration\nAccess time", "heading": "Intances in the Data Store", "level": 3, "chunk_index": 4, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.003163"}, {"id": "en_concepts_5", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Jupyter Notebook\n\nJupyter Notebook is a web-based interactive computing platform that combines live code, equations, narrative text, visualizations, interactive dashboards and other media. Jupyter Notebooks can be used to analyze data stored in an openBIS instance.\n¶", "heading": "Jupyter Notebook", "level": 3, "chunk_index": 5, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.003163"}, {"id": "en_concepts_6", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Lab Notebook\n\nThe Lab Notebook is one of the two main components of openBIS. It is the digital version of a paper lab notebook and can be used for the digital representation and documentation of experimental procedures and analyses and the storage of related data files according to good scientific practice.\nBy default, each user gets their own personal\nSpace\nin the Lab Notebook where they can represent multiple research\nProjects\n. Within a given\nProject\n,\nCollections\ncan be used to represent comprehensive experiments which comprise individual\nObjects\n, e.g., of the type \"Experimental Step\". Access to the personal Lab Notebook\nSpace\nor individual\nProjects\ncan be shared with colleagues.\n¶\nMasterdata\nThe term \"Masterdata\" describes all information structures and plugins that are used to define metadata in openBIS (i.e., masterdata = \"meta-metadata\"). Masterdata is comprised of Entity types, i.e.,\nCollection\n,\nObject\nand\nDataset\ntypes, as well as\nProperty\ntypes,\ncontrolled vocabularies\nand related scripts (e.g., dynamic property plugins and entity validation scripts). Domain-specific masterdata have to be defined by the Data Store Stewards of the BAM divisions, but can only be imported to the openBIS instance (and edited) by Instance Admins.\n¶\nMetadata\nMetadata is \"data about data\" that provides the information needed to find, interpret and understand research data. This includes general\nProperties\nsuch as Code, Name, Description and more specific\nProperties\ndefined by users within Entity Types (for the Data Store starting from rollout phase IV, the only Entity types that are defined are\nObject\nTypes\nwhich might include controlled vocabularies).\nThe following table provides overview on the metadata generated along the openBIS data structure (\nSpace\n,\nProjects\n,\nCollection\n,\nObject\nand\nDataset\n).\n¶\nObject\nIn openBIS, an\nObject\nis an entity with user-defined\nProperties\nlocated on the fourth level of the hierarchical data structure (Space/Project/Collection/\nObject\n).\nAn\nObject\nis always part of a\nCollection\n.\nObjects\nof the same type are described by the same set of\nProperties\n.\nObject\ntypes are defined as part of the openBIS\nmasterdata\n.\nDatasets\ncan be attached to\nObjects\n.\nAn\nObject\ncan be used to represent any kind of physical or intangible entity. For instance, an\nObject\nof the type \"Chemical\" can be used to represent a batch of ethanol in the\nInventory\n. An\nObject\nof the type \"Experimental Step\" can be used to represent a measurement or an analysis in the", "heading": "Lab Notebook", "level": 3, "chunk_index": 6, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.003163"}, {"id": "en_concepts_7", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Parent-Child Relationship\n\nA parent-child relationship is a directed link (or \"directed edge\" in graph theory) between two\nObjects\n(Object1 --> Object2) or between two\nDatasets\n(Dataset1 --> Dataset2) in an openBIS Instance. For a given relationship between two\nObjects\n(or\nDatasets\n), the\nObject\nwith the outgoing edge is called the \"parent\" and the\nObject\nwith the incoming edge is called the \"child\". It is not possible to have parent-child relationships between\nObjects\nand\nDatasets\nor between other entity types (e.g.,\nCollection\n).\nParent-child relationships can be used to represent different kinds of logical connections between\nObjects\n(or\nDatasets", "heading": "Parent-Child Relationship", "level": 3, "chunk_index": 7, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_8", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "a partition of an entity:\n\nObject\n\"Sample 1\" is parent of\nObjects\n\"Sample 1A\" and \"Sample 1B\" because the original sample was broken up into two smaller sub-samples,\ncontext in a research process, e.g., Object \"Experimental Step 1\" is child of the Objects \"Sample 1A\" and \"Measurement Device\" because during the experimental step, the measurement device was used to measure some properties of the sub-sample,\na temporal sequence of different steps in a workflow: Object \"Experimental_Step_1\" is parent of \"Experimental Step 2\" because the first experimental step was conducted prior to the second.\nWhen all of these\nObjects\nand their connections to each other are combined, we get a hierarchy tree (or a \"directed acyclic graph\" (DAG) in graph theory):\nParent-child relationships are not allowed to form cycles within the graph (e.g., an\nObject\ncannot be both parent and child of another\nObject\n), otherwise an error will be reported.\nParent-child relationship can also be used to represent relations between\nDatasets\n, e.g., \"Dataset_v2\" being the parent of \"Dataset_v1\" because the second\nDataset\nis a newer version of the first one.\nParent-child relationships between\nObjects\n(or\nDatasets\n) are independent of the folder hierarchy, i.e.,\nObjects\n(or\nDatasets\n) can be connected across different\nSpaces\nand\nProjects\nand irrespective of whether they are located in the\nInventory\nor the", "heading": "a partition of an entity:", "level": 3, "chunk_index": 8, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_9", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Lab Notebook\n\n.\nBy default, every\nObject\n(or\nDataset\n) can have a unlimited number of parents and/or children or none (N:N relationships with N being any number from 0 to N). For a given\nObject\ntype, group admins can set a minimum and maximum number of children and parents of a certain type in the settings.\n¶\nProject\nIn openBIS, a\nProject\nis a folder located on the second level of the hierarchical data structure (Space/\nProject\n/Collection/Object). A\nProject\nis always part of a\nSpace\n. A\nProject\ncan logically group an unlimited number of\nCollections\n.\nFor instance, a\nProject\n\"Reagents\" can be used to organize\nCollections\nof the type \"Chemicals\" in the\nInventory\n. A Project \"Master Thesis\" can be used to organize\nCollections\nof the type \"Experiment\" in the", "heading": "Lab Notebook", "level": 3, "chunk_index": 9, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_10", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Lab Notebook\n\n.\nApart from a code (PermId) and a description,\nProjects\nhave no metadata. User access rights can be defined at the\nProject\n-level.\n¶\nProperty\nIn openBIS, a\nProperty\nis a metadata field that can be used to describe a\nCollection\n, an\nObject\nor a\nDataset\n.\nProperties\ncan be of different\ndata types\n, e.g., numbers (Boolean, real, integer), text, hyperlink, date,\ncontrolled vocabularies\nbut also tabular data.\n¶\npyBIS\npyBIS is a Python module for interacting with openBIS. Most actions that can be carried out in the openBIS graphical user interface (GUI) can also be done via pyBIS. pyBIS is designed to be most useful in a", "heading": "Lab Notebook", "level": 3, "chunk_index": 10, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_11", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Jupyter Notebook\n\nor IPython environment.\n¶\nRoles defined in openBIS\nThe openBIS roles defined the rights that users get assigned to manage the research data stored in the BAM Data Store. In openBIS, there are four different types of roles, in descending order of rights:\nAdmin\nUser\nObserver\nopenBIS roles are assigned to users at different levels:\nfor the complete openBIS instance (only <PERSON><PERSON> or Observer role)\nfor a\nSpace\nor a\nProject\n: <PERSON><PERSON>, User, Observer", "heading": "Jupyter Notebook", "level": 3, "chunk_index": 11, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_12", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Roles assigned to\n\nSpaces\nand\nProjects\nalso apply to the corresponding subfolders (\nCollections\n,\nObjects\nand\nDatasets\n).\nSince users with the role of Instance Admin  have full access to the entire instance and all\nSpaces\ncontained therein, this role is the sole responsibility of the core team of the Data Store team. Only Instance <PERSON><PERSON> can make changes to the Masterdata, create new\nSpaces\n, and edit the settings of the ELN-LIMS User Interface (UI).\n¶", "heading": "Roles assigned to", "level": 3, "chunk_index": 12, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_13", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Roles and Rights\n\nThe corresponding rights to openBIS User roles are summarized in the table below.\nFor additional information on roles and permissions, please refer to the official openBIS docs\nhere\n.\nRole\nRights", "heading": "Roles and Rights", "level": 3, "chunk_index": 13, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_14", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Instance Admin (Data Store Team)\n\n- Full access to the complete openBIS Instance\n-\nSpace\n/\nProject\nAdmin rights\n- Create and edit Masterdata\n- Create and edit\nSpaces\n- Create/manage\nSpace\nAdmin role", "heading": "Instance <PERSON> (Data Store Team)", "level": 3, "chunk_index": 14, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_15", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Group Admin (Division Head, DSSt)\n\n-\nSpace\n/\nProject\nAdmin rights\n- Customise the group‘s ELN Settings\n- Revert deletions", "heading": "Group Admin (Division Head, DSSt)", "level": 3, "chunk_index": 15, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_16", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Space/Project Admin\n\n-\nSpace\n/\nProject\nUser rights\n- Assign and remove\nSpace\n/\nProject roles\n-Create\nProjects\n-Delete\nProject\ns,\nCollections\n,\nObjects\n,\nDatasets\n- Save searches\nSpace\n/\nProject\nUser\n- Observer rights\n- Create\nCollections\nand\nObjects\n- Edit\nProjects\n,\nCollections\nand\nObjects\nObserver\n- Read-only access\n- Download\nDatasets\n¶", "heading": "Space/Project Admin", "level": 3, "chunk_index": 16, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_17", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Lab Notebook\n\nand\nInventory\nstructure and the multi-groups set up of the Data Store. By default, all division members / users (who are not DSSt or division leads) have the following roles:", "heading": "Lab Notebook", "level": 3, "chunk_index": 17, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_18", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Space/Project Admin\n\nin their personal Lab Notebook\nSpace\n(e.g., X.1 Amueller)\nObserver\nin the personal Lab Notebook of colleagues from the same division", "heading": "Space/Project Admin", "level": 3, "chunk_index": 18, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_19", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Space/Project User\n\nin the private Inventory of their devision (e.g., X.1 EQUIPMENT, X.1 MATERIALS, X.1 METHODS, X.1 PUBLICATIONS)\nObserver\nin the public Inventory of the other divisions (BAM EQUIPMENT, BAM MATERIALS, BAM METHODS, BAM PUBLICATIONS)", "heading": "Space/Project User", "level": 3, "chunk_index": 19, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_20", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Space/Project User\n\nin the public Inventory of their group (X.1 EQUIPMENT OPEN, X.1 MATERIALS OPEN, X.1 METHODS OPEN, X.1 PUBLICATIONS OPEN)\nBy default, all Data Store Stewards (DSSt) and division leads/Group Admins have following roles:", "heading": "Space/Project User", "level": 3, "chunk_index": 20, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_21", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Space/Project Group Admin\n\nin their personal Lab Notebook\nSpace\nand the Lab Notebook of all division members.", "heading": "Space/Project Group Admin", "level": 3, "chunk_index": 21, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_22", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Space/Project Group Admin\n\nin all Inventory Spaces of their division (X.1 EQUIPMENT, X.1 MATERIALS, X.1 METHODS, X.1 PUBLICATIONS).", "heading": "Space/Project Group Admin", "level": 3, "chunk_index": 22, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_23", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Space/Project User\n\nin all public Inventory BAM Spaces (X.1 EQUIPMENT OPEN, X.1 MATERIALS OPEN, X.1 METHODS OPEN, X.1 PUBLICATIONS OPEN)\n¶", "heading": "Space/Project User", "level": 3, "chunk_index": 23, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_24", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Roles Management: Access to Spaces and Projects\n\nopenBIS roles can be assigned to individual users or groups. The main instance of the BAM Data Store is organized as a multi-group instance, with each BAM division (“Fachbereich”) corresponding to a group.\nGroup Admins in openBIS are assigned by default to division heads, their deputies, and Data Store Stewards (DSSt) for all Spaces within their division (group). They are responsible for managing user roles within their group(division), specifically for the Spaces and Projects where they hold Admin rights.\nPlease note that\naccess can only be managed at the\nSpace\nand\nProject\nlevel\nand NOT on the level of individual\nCollections\n,\nObjects\nand/or\nDatasets\n. The rights granted for a\nSpace\napply to all subfolders/entities in the openBIS hierarchical data structure (\nProject\n,\nCollection\n,\nObject\n,\nDataset\n), while rights granted at the Project level apply to all subfolders of the same (\nCollection\n,\nObject\n,\nDataset\n).\nTo manage access rights at the\nSpace\n/\nProject\nlevel in the ELN-LIMS UI, click on the “More” button and select “Manage access”. Note that roles can only be assigned to users or groups that have been granted access by the Instance Admins (Data Store Team).\n¶\nSpace\nIn openBIS, a\nSpace\nis a folder located on the first level of the hierarchical data structure (\nSpace\n/Project/Collection/Object). A\nSpace\nis either located in the\nInventory\nor in the", "heading": "Roles Management: Access to Spaces and Projects", "level": 3, "chunk_index": 24, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_concepts_25", "title": "Concepts of Data Store and openBIS", "url": "https://datastore.bam.de/en/concepts", "source": "datastore", "text": "Lab Notebook\n\n. A\nSpace\ncan logically group an unlimited number of\nProjects\n.\nFor instance, a\nSpace\n\"Materials\" can include the\nProject\n\"Reagents\" in the Inventory. A\nSpace\n\"Master Students\" can include the\nProject\n\"Master Thesis\" in the Lab Notebook.\nApart from the permanent ID (PermId) and a description,\nSpaces\nhave no metadata. User access rights can be defined at the\nSpace\n-level.", "heading": "Lab Notebook", "level": 3, "chunk_index": 25, "file_path": "wikijs\\en_concepts.txt", "timestamp": "2025-09-16T17:51:19.004164"}, {"id": "en_faq_0", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>\n\n06/17/2025\n¶\nGeneral\nWho can use the Data Store? Is the use of the Data Store mandatory?\nIn the future, all BAM employees who work with research data will be able to use the Data Store. Divisions that have registered for the rollout commit themselves to using the Data Store after the end of the onboarding phase. It is the responsibility of the division head to ensure that the Data Store is used appropriately.", "heading": "Meindl, Kristina", "level": 3, "chunk_index": 0, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}, {"id": "en_faq_1", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "Who is responsible for maintaining the Data Store?\n\nThe Data Store is operated as a central service by the central IT (VP.2). Training and consulting is provided by the eScience section (VP.1). The divisions themselves are responsible for the content of their group and maintenance of metadata and data.\nWhat kind of data should be stored and what data should NOT be stored in the Data Store?\nThe Data Store is primarily a system for the internal storage of research data produced at BAM. According to the BAM Data Policy, research data include all digital data that are the basis, object, work steps or result of research processes as well as data that serve to describe the former. Typical examples of research data are measurement and observation data, experimental data obtain in the laboratory, audiovisual information, methodological test procedures and protocols. Simulations, software source code, algorithms, and derivations are also research data that can be stored in the Data Store. However, since the Data Store does not include a version management system, it is recommended to use a more suitable service for software development projects, for example the", "heading": "Who is responsible for maintaining the Data Store?", "level": 3, "chunk_index": 1, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}, {"id": "en_faq_2", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "BAM GitHub\n\n(more information can be found\nhere\nin the Infoportal). It is not allowed to store private data, in particular no personal data, in the Data Store. If you are not sure whether your data may be stored in the Data Store, please contact\n<EMAIL>\n.", "heading": "BAM GitHub", "level": 3, "chunk_index": 2, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}, {"id": "en_faq_3", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "How is the Data Store organized?\n\nThe Data Store is divided into groups that correspond to the BAM divisions. By default, each group receives its own Inventory for the digital representation of laboratory inventory such as measuring instruments, chemical substances, samples, protocols, etc., as well as associated documents (e.g. technical data sheets). By default, each user additionally receives their own Lab Notebook with personal folders for the documentation of research activities and related data. If required, project-based folders (for several project members) can also be created in the Lab Notebook in addition to the personal folders.\nCan closely collaborating divisions have common Spaces in the Data Store Inventory?\nEach division is assigned open Spaces (readable by all Data Store users) and closed Spaces (readable and editable only by division members) in the Data Store Inventory. Group Admins can grant read/write access to the Spaces to members of other divisions to enable joint work. If required, dedicated shared Spaces can be created as well.\nHow are access rights defined within the Data Store?\nThe Data Store is divided into groups, which correspond to BAM divisions. Within a group, roles can be assigned and access rights can be further defined. Users with the appropriate rights can also grant specific access to Spaces and Projects to non-group members. More information about the rights and role system in the Data Store can be found\nhere\n.\nCan changes in the BAM organization (e.g. merging of divisions) be represented in the Data Store?\nThe user accounts in the Data Store as well as the organizational affiliation of the users are derived from the central user directory of BAM (LDAP). If your division is affected by upcoming organizational changes (e.g. renaming or merging) and if the division is  already using the Data Store, please contact\n<EMAIL>\n.\nIs the Data Store/the underlying software openBIS multilingual?\nNo, the user interface of openBIS as well as the official documentation of the ETHZ are only available in English. Training materials for the Data Store are also provided in English. If you need a translation of certain documents/articles, please contact <NAME_EMAIL>. Except for justified exceptions, we also recommend documenting user-defined metadata in openBIS exclusively in English to ensure better interoperability.\nIs the Data Store also suitable for the management of data generated in the context of testing tasks/scientific and technical services (Wissenschaftlich-Technisches Dienstleistungen, WTD)?\nIn principle, any type of data can be stored in the Data Store and described with metadata. Whether the Data Store is also suitable for managing test data will be investigated during the rollout.\nMy data files are very large, can the Data Store handle them?\nThe actual data files are stored in the Data Store as files on disk storage, only the metadata is stored in the database. This means that even very large data volumes are possible, as long as the corresponding storage resources are available. In addition, there is the possibility via git-annex in openBIS to manage only references to datasets when they become too large.\n¶", "heading": "How is the Data Store organized?", "level": 3, "chunk_index": 3, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}, {"id": "en_faq_4", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "IT Infrastructure\n\nWhat happens to the existing file services after the introduction of the Data Store?\nThe personal file services (drive \"M:\") will still be provided. The necessity of shared file services by divisons, project groups, or departments will be reviewed. If required, they will be provided as a supplement to the Data Store.\nWhere is the data stored in the Data Store physically located? Is there be a backup of the Data Store?\nAll data of the Data Store are exclusively stored on servers in the computing center of BAM (UE). The datasets/metadata are backed up regularly/continuously in a multi-stage process.\nWill the Data Store be permanently available?\nAs a central RDM system, the Data Store is designed for permanent operation as far as this is technically possible. However, it will not be possible to avoid interruptions  for maintenance work and updates.\nWill there be access to the Data Store from the lab networks (\"Labornetze\")?\nYes, access from laboratory networks will be enabled where necessary.\nWill there be access for external parties to the Data Store?\nNo, the Data Store is designed as a system for internal research data management at BAM and requires access from internal BAM networks (including VPN). Therefore, external users cannot access the Data Store. However, it is possible to export data from the Data Store, e.g., to the public repository\nZenodo\n.\n¶", "heading": "IT Infrastructure", "level": 3, "chunk_index": 4, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}, {"id": "en_faq_5", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "Data Formats\n\nWhich file formats can be stored in the Data Store?\nopenBIS works independently of file formats: The data are stored as files in the file system, the associated metadata in a database.\nCan I read/work with proprietary file formats in the Data Store?\nThe Data Store is not a live environment for reading or editing files, independent of whether their format is open or proprietary. To read or edit files stored in the Data Store using specialized software, they must be downloaded locally. The modified files can then be re-saved in the Data Store and linked to the original dataset. If the underlying file format allows programmatic access, files stored in the Data Store can also be read and analyzed via APIs (for example, with the Python module\npyBIS\nand Jupyter Notebook), but not modified.\n¶\nMetadata\nWhat kind of metadata standards are used in the Data Store?\nIn the Data Store, individual metadata standards can be defined depending on the research domain. Some basic standards are already offered (e.g. for the description of instruments, chemicals and experimental steps). The definition of additional (domain) metadata standards is an important part of the onboarding process. The divisions are supported in this task by the Data Store team.", "heading": "Data Formats", "level": 3, "chunk_index": 5, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}, {"id": "en_faq_6", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "Can ontologies be represented in the Data Store?\n\nThere is currently no function to import ontologies to openBIS. However, it is possible to add so-called semantic annotations when defining metadata Object and Property Types. However, these are currently not visible in the UI and can only be accessed via API.\n¶\nTraceability\nCan I edit data once it is stored in the Data Store?\nIn the Data Store, there is a clear technical seperation between data (in the form of files) and descriptive metadata. Files are stored in folders, so-called Datasets, which can contain one or more file(s) and whose content is immutable and stored on disk storage. However, Datasets be deleted in their entirety. Metadata entities, on the other hand, are stored in a separate database and can be both edited and deleted.", "heading": "Can ontologies be represented in the Data Store?", "level": 3, "chunk_index": 6, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}, {"id": "en_faq_7", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "Can edits of (meta)data be tracked in the Data Store?\n\nAll edits to the metadata in the Data Store are saved (which change was made when and by whom), so that the entire history of a metadata entry can be traced back if necessary. Files stored in the Data Store, on the other hand, are immutable: They cannot be edited after they have been saved, but they can be deleted.\nCan metadata/data stored in the Data Store be deleted and can deleted metadata/data be recovered?\nUsers with appropriate rights can edit entities as well as delete metadata Objects and associated files (Datasets). Deleted Objects and Datasets are first moved to the openBIS trashcan from where they can be restored if necessary. If Objects/Datasets are removed from the trashcan, they are permanently deleted from the underlying database and cannot be restored. By default, only users with Space Admin and Instance Admin role have permission to delete. If you want to completely prevent editing/deleting an entity, you can irreversibly \"freeze\" individual Objects or Datasets,  as well as entire folders in the Data Store.\nCan chronological relationships be represented in the Lab Notebook of the Data Store?\nYes, by means of directed links between Objects, so-called \"parent-child relationships\", the chronological sequence of, e.g., several experimental steps can be represented in a hierarchy tree. You can find more information about parent-child relationships\nhere\n.\n¶", "heading": "Can edits of (meta)data be tracked in the Data Store?", "level": 3, "chunk_index": 7, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}, {"id": "en_faq_8", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "Data Analysis\n\nWhat are Jupyter Notebooks and how can they be used to analyze data in the Data Store?", "heading": "Data Analysis", "level": 3, "chunk_index": 8, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}, {"id": "en_faq_9", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "Jupyter Notebook\n\nis a web-based interactive computing platform that combines live code, equations, narrative text, visualizations etc. The Jupyter system currently supports over 100 programming languages including Python, Java, R, Julia, Matlab, Octave and many more. Jupyter Notebooks can be used to analyze data stored in an openBIS instance, e.g., by connecting a local Jupyter installation with the Data Store. The output of the analysis and the notebooks themselves can then be saved in the Data Store and connected to the dataset they are based on. It is possible to download an\nextension for JupyterLab\nthat adds three buttons to a default notebook to\nconnect to an openBIS instance,\ndownload datasets from the openBIS instance,\nupload the notebook to the openBIS instance. For researchers using Python for data analysis, we recommend to use\npyBIS\n, a Python module for interacting with openBIS, which is designed to be most useful in a Jupyter Notebook.\n¶", "heading": "Jupyter Notebook", "level": 3, "chunk_index": 9, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}, {"id": "en_faq_10", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "What are the upload options to the Data Store?\n\nFiles can be uploaded to the Data Store in several ways:\nvia the graphical user interface (GUI),\nvia script, e.g., via the Python module\npyBIS\n,\nvia the Dropbox mechanism, where files are copied to a specially created Dropbox folder.\nYou can find more information about the upload via GUI\nhere\n.\nWill there be support for implementing Dropbox scripts for automated data import?\nIt is planned to develop a \"meta dropbox script\" that offers a number of core functionalities (e.g. search for metadata Objects in the Data Store to which the datasets are to be attached; create new Objects and set metadata properties) as well as a template for entering the required metadata. The division must fill this template with the appropriate metadata: either manually or automatically via a parser script that is tailored to a specific data format.\nCan warnings or events be triggered when importing data via Dropbox?\nYes, the Dropbox scripts can validate incoming data and act accordingly.\nCan continuous data streams from measuring devices be included in the Data Store?\nNo, openBIS works with files only. Continuous data streams must be split into individual data files (e.g. per week, day, hour) which can be saved in openBIS as immutable data sets. The integration of openBIS and measuring devices is possible via the so-called Dropbox mechanism. For this, the data files must be saved in a dedicated Dropbox folder; from there they are uploaded to the Data Store. The Dropbox can be controlled via scripts that contain the logic for the data upload. You can find more information\nhere\n.\nCan data be exported/published from the Data Store?\nYes, files and descriptive metadata can be exported, both locally as well as to the public research data repository\nZenodo\n.\n¶\nInterfaces\nWhat interfaces (to devices and software) does the Data Store offer?\nThe APIs of openBIS are described\nhere\nin the openBIS documentation of the ETHZ. There are programming interfaces to Java, Javascript and Python. For data analysis, there is the possibility to connect a local Jupyter installation to the Data Store (Jupyter Notebook, Hub and Lab). Jupyter itself provides numerous kernels to support a variety of programming languages for analyzing data in openBIS.\nWill there be an interface between the Data Store to the hazardous substances database sigmaBAM in order to avoid duplicate work when entering hazardous substances?\nThere is currently no interface between the Data Store and sigmaBAM. Although both systems are intended for the digital representation of chemicals or hazardous substances, the perspective differs: sigmaBAM is used for the documentation of handling permits for hazardous substances as well as for a yearly updated total quantity of a hazardous substance. In the Data Store, on the other hand, it is recommended to represent the specific batch of a chemical and link it to experimental steps to ensure traceability. However, the metadata format for chemicals in the Data Store is based on the metadata format of hazardous substances in sigmaBAM. For divisions that have already entered hazardous substances in sigmaBAM, it is therefore possible to export these as Excel lists and import them (after some adjustments) into the Data Store.\nWill there be an interface from the Data Store to the E-Akte (or vice versa)?\nAt the moment, there are no direct interfaces between the Data Store and the E-Akte, because the two systems are used to store different kinds of data (research data in the Data Store vs. (other) record-relevant documents in the E-Akte). If necessary, files in the E-Akte can be hyperlinked in the Data Store.\nCan data stored in the Data Store be linked to publications in Publica?\nThere are currently no interfaces between the Data Store and Publica. However, it is possible to reference publications in the Data Store, e.g. via hyperlinks to their DOIs.\n¶\nDevelopment\nI have an idea for a new/improved openBIS feature or plugin for the Data Store. Where can I submit this?\nPlease send your ideas for improved and/or additional <NAME_EMAIL>. Depending on whether your idea is a BAM-specific or a general openBIS feature, we will include your suggestion in our feature request list or forward it to the openBIS development team at ETHZ. Please note, however, that due to limited development resources we cannot implement every feature request and therefore prioritize them according to effort/added value. Since openBIS is an open source software, the development team at ETHZ also handles openBIS feature requests at its own discretion.", "heading": "What are the upload options to the Data Store?", "level": 3, "chunk_index": 10, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}, {"id": "en_faq_11", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "Can I develop my own plugins for the Data Store?\n\nIf you would like to participate in the development of plugins for the Data Store, please contact\n<EMAIL>\n.\n¶\nRollout", "heading": "Can I develop my own plugins for the Data Store?", "level": 3, "chunk_index": 11, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}, {"id": "en_faq_12", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "When will the rollout of the Data Store begin?\n\nThe rollout of the Data Store takes place in phases. In each phase, several divisions will be onboarded to the Data Store at the same time. The first rollout phase started in May 2023.\nHow can I register my division for the rollout?\nAll division heads were contacted by the project team in December 2022 as part of a survey to gauge their interest in rollout participation. Based on the responses, the rollout sequence for the first three phases was determined. In February 2025, another survey was conducted, which will now serve as the basis for further phase planning. The divisions involved in phase 4 have already been contacted. If you are a head of division and did not receive the survey, or if you wish to change your response, please contact us at\n<EMAIL>\n.\nWhat is a Data Store Steward/Group Admin and what are the responsibilities associated with the role?\nBefore the Data Store can be used operationally, research group-specific data structures and metadata schemas have be developed. To this end, at least one Data Store Steward must be appointed from each division to take on this task in consultation with colleagues and the division head. The Data Store Stewards are trained and supported in their work by the Data Store project team. Once the Data Store has been implemented for a division, the Data Store Steward acts as Group Admin. Group Admins can access all data and metadata within their own group and (in the case of metadata) edit it, as well as adjust access rights and other settings to meet the requirements of the division. Data Store Stewards are also the point of contact for research data management (RDM) issues within the division and are responsible for introducing new employees to the Data Store once onboarding has been completed. Data Store Stewards should be familiar with methodology and processes within the division and its research domain. Prior IT and RDM experience is helpful, but not required. Ideally, Data Store Stewards should have permanent positions in the division to reduce the risk of knowledge loss upon departure. For larger divisions with subgroups, it is recommended that at least two Data Store Stewards be assigned.\nI would like to take on the role of Data Store Steward/Group Admin for my division, where can I apply for this?\nTo do so, please talk first with the head of your division and then contact us at\n<EMAIL>\n.\nWill there be training and/or manual on how to use the Data Store?\nYes, during onboarding, target group-specific training (for Data Store Stewards and for normal users) will take place. In addition to the openBIS documentation of the ETHZ, accompanying training material will be provided here in the Data Store Wiki.\nHow much time should a division invest for the rollout?\nA rollout phase lasts approx. 2-3 months. During this time, onboarding events are held for the Data Store Steward(s) and for the members of the division. The effort required for adjustments to the Data Store and, if necessary, the setup of interfaces depends on the research subject, the equipment, the working methods, and the FDM requirements of the department and cannot be specified in general terms. The obligations of DSSts include carrying out the onboarding process (approx. 5 working days) and scheduling additional time for system adjustments, as well as integration into daily workflows after onboarding.\nHow can I/my division prepare for the rollout?\nIn preparation for the rollout (and for good research data management in general), it is recommended to consider the following points:\nHow to define uniform rules for naming files within the division (e.g., YYYYMMDD_someMeasurement_Initials_v1.csv)?\nWhat kind of entities should be represented in the Inventory of the Data Store? Can the properties of these entities be structured as standardized metadata in lists? These lists can later be customized and imported into the Data Store.\nWhat kind of information is needed in a typical experiment description? Can this also be standardized in the form of metadata? Can templates for experiment descriptions be created?\nWhat are the links between experimental steps and inventory elements (e.g. an experimental step should be linked with the measuring instrument that was used)?\nWhat kind of output data formats are generated by measurements? Are they open or proprietary (can only be read with special software from the manufacturer)? If open, what kind of measurement metadata are included? Is it possible to write a parser in the form of a short script that automatically reads this metadata?\nWill we get additional staff for the rollout?\nUnfortunately not, the subject-specific implementation and customization of research data management in the Data Store is to be conducted by the divisions themselves. Each division must appoint one or more Data Store Steward(s) for this task. However, the Data Store team from the sections VP.1 (eScience) and VP.2 (IT) will closely accompany the onboarding process and support the Data Store Stewards and the users by offering webinars, Q&A sessions and training materials.\nCan I test openBIS before my division is onboarded to the Data Store?\nThree demo openBIS instances of ETHZ and EMPA are available\nhere\n.\nIn future, we will provide a demo instance of the Data Store. If you are interested in testing it, please contact the Data Store team at\n<EMAIL>\n.\nThe openBIS installer as well as a pre-installed virtual machine image is available\nhere\n. For the download, a registration at the SIS helpdesk is necessary. A\ncontainer image\nis also provided. It should be noted, however, that the entire functionality only becomes visible after a complex configuration process and not all features can be easily grasped in a demo installation.\nCan I participate in the Data Store rollout as an individual employee?\nNo, the rollout of the Data Store is designed for divisions and not for individual employees.\n¶", "heading": "When will the rollout of the Data Store begin?", "level": 3, "chunk_index": 12, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}, {"id": "en_faq_13", "title": "Frequently Asked Questions (FAQ)", "url": "https://datastore.bam.de/en/faq", "source": "datastore", "text": "Pilot Phase\n\nMy research group was part of the pilot project. What happens to the pilot instance during the rollout?\nAs agreed at the beginning of the pilot project, the pilot instances will continue to be supported, provided with security updates and kept operational, but not provided with new features (which go beyond the regular openBIS updates) or further customised. In the medium term, it is planned to transfer the data of the pilot instances to the BAM-wide system.\nWill more pilot groups be onboarded as test cases for the Data Store?\nNo, the pilot project was completed in February 2022 and no additional pilot groups will be included. All further groups will be included in the rollout, i.e. the central implementation of the Data Store, which started in May 2023.", "heading": "Pilot Phase", "level": 3, "chunk_index": 13, "file_path": "wikijs\\en_faq.txt", "timestamp": "2025-09-16T17:51:19.010796"}]