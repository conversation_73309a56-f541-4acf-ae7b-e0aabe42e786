#!/usr/bin/env python3
"""
Examine the actual HTML to understand the link structure.
"""

import requests
from bs4 import <PERSON><PERSON>oup

def examine_html():
    """Examine the HTML structure."""
    
    url = "https://openbis.readthedocs.io/en/20.10.0-11/index.html"
    
    print(f"Fetching: {url}")
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, "html.parser")
        
        print(f"Page title: {soup.title.string if soup.title else 'No title'}")
        
        # Find all links
        links = soup.find_all("a", href=True)
        print(f"\nFound {len(links)} links total")
        
        # Look for documentation links specifically
        doc_links = []
        for link in links:
            href = link["href"]
            text = link.get_text(strip=True)
            
            # Look for documentation-related links
            if any(keyword in href.lower() for keyword in ["user-documentation", "software-developer", "system-documentation"]):
                doc_links.append((href, text))
            elif any(keyword in text.lower() for keyword in ["general users", "admin users", "advance features", "development", "apis"]):
                doc_links.append((href, text))
        
        print(f"\nDocumentation links found ({len(doc_links)}):")
        for href, text in doc_links:
            print(f"  '{href}' -> '{text}'")
        
        # Also check for any links that start with /en/
        en_links = [link["href"] for link in links if link["href"].startswith("/en/")]
        print(f"\nLinks starting with /en/ ({len(en_links)}):")
        for href in en_links[:10]:  # Show first 10
            print(f"  '{href}'")
        
        # Check for relative links
        relative_links = [link["href"] for link in links if not link["href"].startswith(("http", "/", "#"))]
        print(f"\nRelative links ({len(relative_links)}):")
        for href in relative_links[:10]:  # Show first 10
            print(f"  '{href}'")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    examine_html()
