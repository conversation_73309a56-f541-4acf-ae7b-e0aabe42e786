Title: Enable archiving to Long Term Storage
URL: https://openbis.readthedocs.io/en/20.10.0-11/user-documentation/general-admin-users/admins-documentation/enable-archiving-to-long-term-storage.html
Source: openbis
---

Enable archiving to Long Term Storage

openBIS supports archiving of datasets to Strongbox and StronLink
(
https://www.strongboxdata.com/
) as
described in
Datasets
Archiving
This needs to be set up and configured on
system level
.
Archiving can be manually triggered from the ELN interface. By default,
the archiving buttons are not shown, and they need to be enabled by an
Instance admin
or even a
group admin
in the case of a multi-group
instance. This is done in the ELN Settings, as shown below.
In addition, the Unarchiving helper tool should also be enabled in the
ELN Settings:
More information on archiving and unarchiving datasets can be found
here:
Data
archiving
Updated on April 26, 2023