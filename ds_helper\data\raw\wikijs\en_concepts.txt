Title: Concepts of Data Store and openBIS
URL: https://datastore.bam.de/en/concepts
Source: datastore
---

/
concepts
Concepts of Data Store and openBIS
Page Contents
Collection
Controlled Vocabulary
Dataset
Data Structure
Dropbox
Entity and Entity Types
Inventory
Intances in the Data Store
Jupyter Notebook
Lab Notebook
Masterdata
Metadata
Object
Parent-Child Relationship
Project
Property
pyBIS
Roles defined in openBIS
Roles and Rights
Roles defined per default in the Data Store
Roles Management: Access to Spaces and Projects
Space
Last edited by
<PERSON><PERSON><PERSON><PERSON>, <PERSON>
Last Friday at 11:54 AM
¶
Collection
In openBIS, a
Collection
is a folder with user-defined
Properties
located on the third level of the hierarchical data structure (Space/Project/
Collection
/Object). A
Collection
is always part of a
Project
.
Collections
of the same type are described by the same set of
Properties
.
Collection
types are defined as part of the openBIS
masterdata
.
Datasets
can be attached to
Collections
.
A
Collection
can logically group an unlimited number of
Object
of one or more
Object
types. For instance, a
Collection
of the type "Measurement Devices" can be used to organize
Objects
of the type "Instrument" in the
Inventory
. A
Collection
of the type "Default Experiment" can be used to organize
Objects
of the type "Experimental Step" in the
Lab Notebook
.
¶
Controlled Vocabulary
A controlled vocabulary is an established list of terms to provide consistency and uniqueness in the description of a given domain, e.g., a list of room labels, SI units or purity grades. In openBIS, controlled vocabularies are a possible data type for metadata
Properties
. Each term in a controlled vocabulary has a code, a label, and a description. All existing controlled vocabularies and their terms are listed in the Vocabulary Browser in the Utilities.
¶
Dataset
In openBIS, a
Dataset
is a folder with user-defined
Properties
that can contain individual files of arbitrary formats (e.g., images, csv files, xml files, etc.) as well as complex folder structures with subfolders and many files (of potentially different formats). The content of
Datasets
(but not their metadata) is immutable, i.e., it cannot be edited after creation.
Datasets
of the same type are described by the same set of
Properties
.
Dataset
types are defined as part of the openBIS
masterdata
.
A
Dataset
has to be attached to either an
Object
or to a
Collection
. Different
Datasets
can be connected via
parent-child relationships
.
¶
Data Structure
The openBIS data structure is hierarchically organized in five (sub)folders called
Space
,
Project
,
Collection
,
Object
and
Dataset
. Folder names can be customized by users.
To digitally represent an
Object
, a
Space
,
Project
and
Collection
need to be created first.
¶
Dropbox
The Dropbox is a core openBIS plugin that allows the upload of (large) data files to an openBIS instance. Instead of using the user interface for
Dataset
registration, users move their data files (+ information about the
Object
or
Collection
they should be attached to) to a dedicated Dropbox folder in a file-service at BAM that is continously monitored. Once new data files are detected inside the Dropbox folder, they are automatically uploaded as
Datasets
to the openBIS instance.
It is possible to control the
Dataset
registration process via Dropbox scripts written in Python. The script can register new
Datasets
,
Objects
,
Properties
and
parent-child relationships
as part of its processing. The Dropbox framework also provides tools to track file operations and, if necessary, revert them, ensuring that the incoming file or directory is returned to its original state in the event of an error.
The Dropbox is not related to the commercial file hosting service.
¶
Entity and Entity Types
An Entity is an item of the "real world" (tangible/non tangible) that is uniquely identified by attributes (
Properties
). An Entity Type is a collection of Entities with similar properties. An Entity Type is an object in a data model. In openBIS relevant entity types are
Collection
,
Object
and
Dataset
types. Entity types can only be created by someone with the Instance admin role.
¶
Inventory
The Inventory is one of the two main components of openBIS. It is used for the digital representation of shared laboratory inventory and the storage of related data files such as measuring instruments, chemical substances, and samples, but can also be user for storing protocols, standard operating procedures (SOPs) and publications. The Inventory is organized into
Spaces
,
Projects
and
Collections
according to the hierarchical
data structure
of openBIS.
By default, each BAM division gets
private
Inventory
Spaces
(Equipment, Materials, Methods, Publications) that are only accessible to division members
public
Inventory
Projects
(Equipment, Materials, Methods) that are accessible to every user of the Data Store.
¶
Intances in the Data Store
In the Data Store, various instances are available to support users during different stages of the onboarding and data management process. Below are two key instances and their roles:
Instance
Users
Configuration
Access time
Training Data Store
DSSt during the onbodring
Multi-group instance
Only during onboarding
Main Data Store
Onboarded divisions
Multi-group instance
After onboarding continously
¶
Jupyter Notebook
Jupyter Notebook is a web-based interactive computing platform that combines live code, equations, narrative text, visualizations, interactive dashboards and other media. Jupyter Notebooks can be used to analyze data stored in an openBIS instance.
¶
Lab Notebook
The Lab Notebook is one of the two main components of openBIS. It is the digital version of a paper lab notebook and can be used for the digital representation and documentation of experimental procedures and analyses and the storage of related data files according to good scientific practice.
By default, each user gets their own personal
Space
in the Lab Notebook where they can represent multiple research
Projects
. Within a given
Project
,
Collections
can be used to represent comprehensive experiments which comprise individual
Objects
, e.g., of the type "Experimental Step". Access to the personal Lab Notebook
Space
or individual
Projects
can be shared with colleagues.
¶
Masterdata
The term "Masterdata" describes all information structures and plugins that are used to define metadata in openBIS (i.e., masterdata = "meta-metadata"). Masterdata is comprised of Entity types, i.e.,
Collection
,
Object
and
Dataset
types, as well as
Property
types,
controlled vocabularies
and related scripts (e.g., dynamic property plugins and entity validation scripts). Domain-specific masterdata have to be defined by the Data Store Stewards of the BAM divisions, but can only be imported to the openBIS instance (and edited) by Instance Admins.
¶
Metadata
Metadata is "data about data" that provides the information needed to find, interpret and understand research data. This includes general
Properties
such as Code, Name, Description and more specific
Properties
defined by users within Entity Types (for the Data Store starting from rollout phase IV, the only Entity types that are defined are
Object
Types
which might include controlled vocabularies).
The following table provides overview on the metadata generated along the openBIS data structure (
Space
,
Projects
,
Collection
,
Object
and
Dataset
).
¶
Object
In openBIS, an
Object
is an entity with user-defined
Properties
located on the fourth level of the hierarchical data structure (Space/Project/Collection/
Object
).
An
Object
is always part of a
Collection
.
Objects
of the same type are described by the same set of
Properties
.
Object
types are defined as part of the openBIS
masterdata
.
Datasets
can be attached to
Objects
.
An
Object
can be used to represent any kind of physical or intangible entity. For instance, an
Object
of the type "Chemical" can be used to represent a batch of ethanol in the
Inventory
. An
Object
of the type "Experimental Step" can be used to represent a measurement or an analysis in the
Lab Notebook
.
¶
Parent-Child Relationship
A parent-child relationship is a directed link (or "directed edge" in graph theory) between two
Objects
(Object1 --> Object2) or between two
Datasets
(Dataset1 --> Dataset2) in an openBIS Instance. For a given relationship between two
Objects
(or
Datasets
), the
Object
with the outgoing edge is called the "parent" and the
Object
with the incoming edge is called the "child". It is not possible to have parent-child relationships between
Objects
and
Datasets
or between other entity types (e.g.,
Collection
).
Parent-child relationships can be used to represent different kinds of logical connections between
Objects
(or
Datasets
), e.g.:
a partition of an entity:
Object
"Sample 1" is parent of
Objects
"Sample 1A" and "Sample 1B" because the original sample was broken up into two smaller sub-samples,
context in a research process, e.g., Object "Experimental Step 1" is child of the Objects "Sample 1A" and "Measurement Device" because during the experimental step, the measurement device was used to measure some properties of the sub-sample,
a temporal sequence of different steps in a workflow: Object "Experimental_Step_1" is parent of "Experimental Step 2" because the first experimental step was conducted prior to the second.
When all of these
Objects
and their connections to each other are combined, we get a hierarchy tree (or a "directed acyclic graph" (DAG) in graph theory):
Parent-child relationships are not allowed to form cycles within the graph (e.g., an
Object
cannot be both parent and child of another
Object
), otherwise an error will be reported.
Parent-child relationship can also be used to represent relations between
Datasets
, e.g., "Dataset_v2" being the parent of "Dataset_v1" because the second
Dataset
is a newer version of the first one.
Parent-child relationships between
Objects
(or
Datasets
) are independent of the folder hierarchy, i.e.,
Objects
(or
Datasets
) can be connected across different
Spaces
and
Projects
and irrespective of whether they are located in the
Inventory
or the
Lab Notebook
.
By default, every
Object
(or
Dataset
) can have a unlimited number of parents and/or children or none (N:N relationships with N being any number from 0 to N). For a given
Object
type, group admins can set a minimum and maximum number of children and parents of a certain type in the settings.
¶
Project
In openBIS, a
Project
is a folder located on the second level of the hierarchical data structure (Space/
Project
/Collection/Object). A
Project
is always part of a
Space
. A
Project
can logically group an unlimited number of
Collections
.
For instance, a
Project
"Reagents" can be used to organize
Collections
of the type "Chemicals" in the
Inventory
. A Project "Master Thesis" can be used to organize
Collections
of the type "Experiment" in the
Lab Notebook
.
Apart from a code (PermId) and a description,
Projects
have no metadata. User access rights can be defined at the
Project
-level.
¶
Property
In openBIS, a
Property
is a metadata field that can be used to describe a
Collection
, an
Object
or a
Dataset
.
Properties
can be of different
data types
, e.g., numbers (Boolean, real, integer), text, hyperlink, date,
controlled vocabularies
but also tabular data.
¶
pyBIS
pyBIS is a Python module for interacting with openBIS. Most actions that can be carried out in the openBIS graphical user interface (GUI) can also be done via pyBIS. pyBIS is designed to be most useful in a
Jupyter Notebook
or IPython environment.
¶
Roles defined in openBIS
The openBIS roles defined the rights that users get assigned to manage the research data stored in the BAM Data Store. In openBIS, there are four different types of roles, in descending order of rights:
Admin
User
Observer
openBIS roles are assigned to users at different levels:
for the complete openBIS instance (only Admin or Observer role)
for a
Space
or a
Project
: Adin, User, Observer
Roles assigned to
Spaces
and
Projects
also apply to the corresponding subfolders (
Collections
,
Objects
and
Datasets
).
Since users with the role of Instance Admin  have full access to the entire instance and all
Spaces
contained therein, this role is the sole responsibility of the core team of the Data Store team. Only Instance Admins can make changes to the Masterdata, create new
Spaces
, and edit the settings of the ELN-LIMS User Interface (UI).
¶
Roles and Rights
The corresponding rights to openBIS User roles are summarized in the table below.
For additional information on roles and permissions, please refer to the official openBIS docs
here
.
Role
Rights
Instance Admin (Data Store Team)
- Full access to the complete openBIS Instance
-
Space
/
Project
Admin rights
- Create and edit Masterdata
- Create and edit
Spaces
- Create/manage
Space
Admin role
Group Admin (Division Head, DSSt)
-
Space
/
Project
Admin rights
- Customise the group‘s ELN Settings
- Revert deletions
Space/Project Admin
-
Space
/
Project
User rights
- Assign and remove
Space
/
Project roles
-Create
Projects
-Delete
Project
s,
Collections
,
Objects
,
Datasets
- Save searches
Space
/
Project
User
- Observer rights
- Create
Collections
and
Objects
- Edit
Projects
,
Collections
and
Objects
Observer
- Read-only access
- Download
Datasets
¶
Roles defined per default in the Data Store
Default roles are based on the
Lab Notebook
and
Inventory
structure and the multi-groups set up of the Data Store. By default, all division members / users (who are not DSSt or division leads) have the following roles:
Space/Project Admin
in their personal Lab Notebook
Space
(e.g., X.1 Amueller)
Observer
in the personal Lab Notebook of colleagues from the same division
Space/Project User
in the private Inventory of their devision (e.g., X.1 EQUIPMENT, X.1 MATERIALS, X.1 METHODS, X.1 PUBLICATIONS)
Observer
in the public Inventory of the other divisions (BAM EQUIPMENT, BAM MATERIALS, BAM METHODS, BAM PUBLICATIONS)
Space/Project User
in the public Inventory of their group (X.1 EQUIPMENT OPEN, X.1 MATERIALS OPEN, X.1 METHODS OPEN, X.1 PUBLICATIONS OPEN)
By default, all Data Store Stewards (DSSt) and division leads/Group Admins have following roles:
Space/Project Group Admin
in their personal Lab Notebook
Space
and the Lab Notebook of all division members.
Space/Project Group Admin
in all Inventory Spaces of their division (X.1 EQUIPMENT, X.1 MATERIALS, X.1 METHODS, X.1 PUBLICATIONS).
Space/Project User
in all public Inventory BAM Spaces (X.1 EQUIPMENT OPEN, X.1 MATERIALS OPEN, X.1 METHODS OPEN, X.1 PUBLICATIONS OPEN)
¶
Roles Management: Access to Spaces and Projects
openBIS roles can be assigned to individual users or groups. The main instance of the BAM Data Store is organized as a multi-group instance, with each BAM division (“Fachbereich”) corresponding to a group.
Group Admins in openBIS are assigned by default to division heads, their deputies, and Data Store Stewards (DSSt) for all Spaces within their division (group). They are responsible for managing user roles within their group(division), specifically for the Spaces and Projects where they hold Admin rights.
Please note that
access can only be managed at the
Space
and
Project
level
and NOT on the level of individual
Collections
,
Objects
and/or
Datasets
. The rights granted for a
Space
apply to all subfolders/entities in the openBIS hierarchical data structure (
Project
,
Collection
,
Object
,
Dataset
), while rights granted at the Project level apply to all subfolders of the same (
Collection
,
Object
,
Dataset
).
To manage access rights at the
Space
/
Project
level in the ELN-LIMS UI, click on the “More” button and select “Manage access”. Note that roles can only be assigned to users or groups that have been granted access by the Instance Admins (Data Store Team).
¶
Space
In openBIS, a
Space
is a folder located on the first level of the hierarchical data structure (
Space
/Project/Collection/Object). A
Space
is either located in the
Inventory
or in the
Lab Notebook
. A
Space
can logically group an unlimited number of
Projects
.
For instance, a
Space
"Materials" can include the
Project
"Reagents" in the Inventory. A
Space
"Master Students" can include the
Project
"Master Thesis" in the Lab Notebook.
Apart from the permanent ID (PermId) and a description,
Spaces
have no metadata. User access rights can be defined at the
Space
-level.