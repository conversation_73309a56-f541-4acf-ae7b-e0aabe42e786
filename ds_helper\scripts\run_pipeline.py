#!/usr/bin/env python3
"""
Complete pipeline script for DS Helper.

This script runs the complete pipeline:
1. Scrape ReadTheDocs (openBIS documentation)
2. Scrape Wiki.js (Data Store wiki) - if configured
3. Process all scraped content
4. Ingest into ChromaDB vector database
5. Optionally start the query interface
"""

import argparse
import logging
import os
import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from ds_helper.scraper.cli import parse_args as scraper_parse_args, run_with_args as scraper_run
from ds_helper.processor.cli import parse_args as processor_parse_args, run_with_args as processor_run
from ds_helper.query.cli import parse_args as query_parse_args, run_with_args as query_run
from ds_helper.utils.logging import setup_logging
from ingest_to_vectordb import main as ingest_main

# Configure logging
logger = logging.getLogger(__name__)


def main():
    """Main function to run the complete pipeline."""
    parser = argparse.ArgumentParser(
        description="Run the complete DS Helper pipeline"
    )
    parser.add_argument(
        "--openbis-url",
        default="https://openbis.readthedocs.io/en/20.10.0-11/",
        help="URL of the openBIS ReadTheDocs site"
    )
    parser.add_argument(
        "--wikijs-url",
        help="URL of the Wiki.js site (optional)"
    )
    parser.add_argument(
        "--output-dir",
        default="ds_helper/data",
        help="Base directory for output data"
    )
    parser.add_argument(
        "--db-path",
        default="ds_helper_vectordb",
        help="Path to the ChromaDB database directory"
    )
    parser.add_argument(
        "--max-pages",
        type=int,
        default=50,
        help="Maximum number of pages to scrape from each source"
    )
    parser.add_argument(
        "--skip-scraping",
        action="store_true",
        help="Skip scraping and only process existing data"
    )
    parser.add_argument(
        "--skip-processing",
        action="store_true",
        help="Skip processing and only ingest existing processed data"
    )
    parser.add_argument(
        "--reset-db",
        action="store_true",
        help="Reset the vector database before ingesting"
    )
    parser.add_argument(
        "--start-query",
        action="store_true",
        help="Start the query interface after pipeline completion"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )

    args = parser.parse_args()

    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    setup_logging(log_level)

    # Create output directories
    raw_dir = os.path.join(args.output_dir, "raw")
    processed_dir = os.path.join(args.output_dir, "processed")
    os.makedirs(raw_dir, exist_ok=True)
    os.makedirs(processed_dir, exist_ok=True)

    try:
        # Step 1: Scraping
        if not args.skip_scraping:
            logger.info("=== STEP 1: SCRAPING ===")
            
            # Scrape ReadTheDocs (openBIS)
            logger.info(f"Scraping openBIS documentation from {args.openbis_url}")
            rtd_output_dir = os.path.join(raw_dir, "openbis")
            os.makedirs(rtd_output_dir, exist_ok=True)
            
            scraper_args = scraper_parse_args([
                "readthedocs",
                "--url", args.openbis_url,
                "--output", rtd_output_dir,
                "--max-pages", str(args.max_pages),
                "--verbose" if args.verbose else ""
            ])
            
            result = scraper_run(scraper_args)
            if result != 0:
                logger.error("ReadTheDocs scraping failed")
                return result

            # Scrape Wiki.js if URL provided
            if args.wikijs_url:
                logger.info(f"Scraping Wiki.js from {args.wikijs_url}")
                wiki_output_dir = os.path.join(raw_dir, "wikijs")
                os.makedirs(wiki_output_dir, exist_ok=True)
                
                scraper_args = scraper_parse_args([
                    "wikijs",
                    "--url", args.wikijs_url,
                    "--output", wiki_output_dir,
                    "--max-pages", str(args.max_pages),
                    "--verbose" if args.verbose else ""
                ])
                
                result = scraper_run(scraper_args)
                if result != 0:
                    logger.error("Wiki.js scraping failed")
                    return result
            else:
                logger.info("No Wiki.js URL provided, skipping Wiki.js scraping")
        else:
            logger.info("Skipping scraping step")

        # Step 2: Processing
        if not args.skip_processing:
            logger.info("=== STEP 2: PROCESSING ===")
            logger.info(f"Processing scraped content from {raw_dir}")
            
            processor_args = processor_parse_args([
                "--input", raw_dir,
                "--output", processed_dir,
                "--verbose" if args.verbose else ""
            ])
            
            result = processor_run(processor_args)
            if result != 0:
                logger.error("Processing failed")
                return result
        else:
            logger.info("Skipping processing step")

        # Step 3: Vector Database Ingestion
        logger.info("=== STEP 3: VECTOR DATABASE INGESTION ===")
        chunks_file = os.path.join(processed_dir, "chunks.json")
        
        if not os.path.exists(chunks_file):
            logger.error(f"Chunks file not found: {chunks_file}")
            return 1

        logger.info("Ingesting processed content into vector database")
        
        # Save original argv and modify for ingestion script
        original_argv = sys.argv.copy()
        sys.argv = [
            "ingest_to_vectordb",
            "--chunks-file", chunks_file,
            "--db-path", args.db_path,
            "--verbose" if args.verbose else ""
        ]
        
        if args.reset_db:
            sys.argv.append("--reset")
        
        result = ingest_main()
        sys.argv = original_argv  # Restore original argv
        
        if result != 0:
            logger.error("Vector database ingestion failed")
            return result

        # Step 4: Start Query Interface (optional)
        if args.start_query:
            logger.info("=== STEP 4: STARTING QUERY INTERFACE ===")
            logger.info("Starting DS Helper query interface")
            
            query_args = query_parse_args([
                "--db-path", args.db_path,
                "--verbose" if args.verbose else ""
            ])
            
            return query_run(query_args)
        else:
            logger.info("Pipeline completed successfully!")
            logger.info(f"Vector database created at: {args.db_path}")
            logger.info("You can now start the query interface with:")
            logger.info(f"  python main.py query --db-path {args.db_path}")
            return 0

    except Exception as e:
        logger.error(f"Pipeline failed with error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
