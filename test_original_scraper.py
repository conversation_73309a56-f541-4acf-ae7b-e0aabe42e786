#!/usr/bin/env python3
"""
Test the original chatBIS scraper with the new URL.
"""

import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from chatBIS.scraper.scraper import ReadTheDocsScraper
from chatBIS.processor.processor import RAGProcessor
from chatBIS.utils.logging import setup_logging

def test_original_implementation():
    """Test the original implementation with the new URL."""
    import logging
    setup_logging(logging.INFO)
    
    print("=== Testing Original ReadTheDocs Scraper ===")
    
    # Test scraper with new URL
    scraper = ReadTheDocsScraper(
        base_url="https://openbis.readthedocs.io/en/20.10.0-11/",
        output_dir="test_original_output",
        max_pages=5
    )
    
    scraper.scrape()
    
    # Check scraped files
    output_dir = Path("test_original_output")
    if output_dir.exists():
        scraped_files = list(output_dir.glob("*.txt"))
        print(f"Scraped {len(scraped_files)} files:")
        
        for f in scraped_files:
            print(f"  - {f.name}")
            with open(f, 'r', encoding='utf-8') as file:
                content = file.read()
                print(f"    Size: {len(content)} characters")
                print(f"    First 200 chars: {content[:200]}")
                print("    ---")
        
        print("\n=== Testing Original Processor ===")
        
        # Test processor
        processor = RAGProcessor(
            input_dir="test_original_output",
            output_dir="test_original_processed",
            min_chunk_size=50,  # Smaller for testing
            max_chunk_size=500
        )
        
        processor.process()
        
        # Check processed files
        processed_dir = Path("test_original_processed")
        chunks_file = processed_dir / "chunks.json"
        if chunks_file.exists():
            import json
            with open(chunks_file, 'r', encoding='utf-8') as f:
                chunks = json.load(f)
            print(f"Created {len(chunks)} chunks")
            
            if chunks:
                sample = chunks[0]
                print("Sample chunk:")
                for key, value in sample.items():
                    if key == 'embedding':
                        print(f"  {key}: [vector of length {len(value)}]")
                    else:
                        print(f"  {key}: {value}")
        else:
            print("No chunks.json created")
    else:
        print("No output directory created")

if __name__ == "__main__":
    test_original_implementation()
