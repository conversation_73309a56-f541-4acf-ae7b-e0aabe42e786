#!/usr/bin/env python3
"""
Scraped Text Processor for DS Helper

This module processes the text files scraped by the ReadTheDocs and Wiki.js scrapers
and applies intelligent chunking for use in RAG pipelines. It handles the specific
format produced by the ds_helper scrapers and creates well-structured chunks.
"""

import json
import logging
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)


class ScrapedTextChunker:
    """Intelligent chunker for scraped text content."""

    def __init__(
        self,
        min_chunk_size: int = 100,
        max_chunk_size: int = 1000,
        chunk_overlap: int = 50
    ):
        """
        Initialize the chunker.

        Args:
            min_chunk_size: Minimum size of a chunk in characters
            max_chunk_size: Maximum size of a chunk in characters
            chunk_overlap: Overlap between chunks in characters
        """
        self.min_chunk_size = min_chunk_size
        self.max_chunk_size = max_chunk_size
        self.chunk_overlap = chunk_overlap

    def _detect_structure(self, content: str) -> List[Tuple[str, str, int]]:
        """
        Detect structural elements in the content.
        
        Returns:
            List of (heading_text, content_text, level) tuples
        """
        lines = content.split('\n')
        sections = []
        current_section = {"heading": "Introduction", "content": [], "level": 0}
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Detect various heading patterns
            heading_level = self._detect_heading_level(line)
            
            if heading_level > 0:
                # Save previous section if it has content
                if current_section["content"]:
                    content_text = '\n'.join(current_section["content"]).strip()
                    if content_text:
                        sections.append((
                            current_section["heading"],
                            content_text,
                            current_section["level"]
                        ))
                
                # Start new section
                current_section = {
                    "heading": line,
                    "content": [],
                    "level": heading_level
                }
            else:
                current_section["content"].append(line)
        
        # Add the last section
        if current_section["content"]:
            content_text = '\n'.join(current_section["content"]).strip()
            if content_text:
                sections.append((
                    current_section["heading"],
                    content_text,
                    current_section["level"]
                ))
        
        return sections

    def _detect_heading_level(self, line: str) -> int:
        """
        Detect if a line is a heading and return its level.
        
        Returns:
            0 if not a heading, 1-6 for heading levels
        """
        line = line.strip()
        
        # Markdown-style headings
        if line.startswith('#'):
            return min(len(line) - len(line.lstrip('#')), 6)
        
        # All caps lines (likely headings)
        if len(line) > 3 and line.isupper() and not line.isdigit():
            return 2
        
        # Lines that end with colon and are short (likely section headers)
        if line.endswith(':') and len(line) < 80 and len(line.split()) <= 8:
            return 3
        
        # Lines that are standalone and look like titles
        if (len(line) < 100 and 
            len(line.split()) <= 10 and 
            line[0].isupper() and 
            not line.endswith('.') and
            not line.startswith('http')):
            # Check if it looks like a title (title case or important words)
            words = line.split()
            if len(words) >= 2:
                title_words = sum(1 for word in words if word[0].isupper() or word.lower() in 
                                ['the', 'and', 'or', 'of', 'in', 'on', 'at', 'to', 'for', 'with'])
                if title_words >= len(words) * 0.6:
                    return 3
        
        return 0

    def chunk_content(self, content: str, title: str = "") -> List[Dict[str, str]]:
        """
        Chunk the content intelligently based on structure.

        Args:
            content: The content to chunk
            title: The document title for context

        Returns:
            List of chunk dictionaries with text and metadata
        """
        sections = self._detect_structure(content)
        chunks = []
        
        if not sections:
            # Fallback: simple paragraph-based chunking
            return self._simple_chunk(content, title)
        
        for heading, section_content, level in sections:
            section_chunks = self._chunk_section(heading, section_content, level)
            chunks.extend(section_chunks)
        
        return chunks

    def _chunk_section(self, heading: str, content: str, level: int) -> List[Dict[str, str]]:
        """Chunk a single section."""
        chunks = []
        
        # Split content into paragraphs
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        if not paragraphs:
            return chunks
        
        current_chunk = heading
        current_size = len(heading)
        
        for paragraph in paragraphs:
            paragraph_size = len(paragraph)
            
            # If adding this paragraph would exceed max size and we have enough content
            if (current_size + paragraph_size > self.max_chunk_size and 
                current_size >= self.min_chunk_size):
                
                chunks.append({
                    "text": current_chunk.strip(),
                    "heading": heading,
                    "level": level
                })
                
                # Start new chunk with heading and overlap
                current_chunk = heading + "\n\n" + paragraph
                current_size = len(current_chunk)
            else:
                # Add paragraph to current chunk
                current_chunk += "\n\n" + paragraph
                current_size += paragraph_size + 2  # +2 for newlines
        
        # Add the final chunk if it meets minimum size
        if current_size >= self.min_chunk_size:
            chunks.append({
                "text": current_chunk.strip(),
                "heading": heading,
                "level": level
            })
        
        return chunks

    def _simple_chunk(self, content: str, title: str) -> List[Dict[str, str]]:
        """Fallback simple chunking when no structure is detected."""
        chunks = []
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        current_chunk = ""
        
        for paragraph in paragraphs:
            if (len(current_chunk) + len(paragraph) > self.max_chunk_size and 
                len(current_chunk) >= self.min_chunk_size):
                
                chunks.append({
                    "text": current_chunk.strip(),
                    "heading": title or "Content",
                    "level": 1
                })
                current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
        
        if current_chunk and len(current_chunk) >= self.min_chunk_size:
            chunks.append({
                "text": current_chunk.strip(),
                "heading": title or "Content",
                "level": 1
            })
        
        return chunks


class ScrapedTextProcessor:
    """Main processor for scraped text files."""

    def __init__(
        self,
        input_dir: str,
        output_dir: str,
        min_chunk_size: int = 100,
        max_chunk_size: int = 1000,
        chunk_overlap: int = 50
    ):
        """
        Initialize the processor.

        Args:
            input_dir: Directory containing scraped .txt files
            output_dir: Directory to save processed chunks
            min_chunk_size: Minimum chunk size in characters
            max_chunk_size: Maximum chunk size in characters
            chunk_overlap: Overlap between chunks in characters
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.chunker = ScrapedTextChunker(
            min_chunk_size=min_chunk_size,
            max_chunk_size=max_chunk_size,
            chunk_overlap=chunk_overlap
        )
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def parse_scraped_file(self, file_path: Path) -> Dict[str, str]:
        """
        Parse a scraped .txt file to extract metadata and content.
        
        Returns:
            Dictionary with title, url, source, and content
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Split metadata and content
        if '---\n\n' in content:
            metadata_part, content_part = content.split('---\n\n', 1)
        else:
            # Fallback if format is different
            lines = content.split('\n')
            metadata_part = '\n'.join(lines[:10])  # First 10 lines
            content_part = '\n'.join(lines[10:])
        
        # Parse metadata
        metadata = {}
        for line in metadata_part.split('\n'):
            if ':' in line:
                key, value = line.split(':', 1)
                metadata[key.strip().lower()] = value.strip()
        
        return {
            'title': metadata.get('title', file_path.stem),
            'url': metadata.get('url', ''),
            'source': metadata.get('source', 'unknown'),
            'content': content_part.strip()
        }

    def process_file(self, file_path: Path) -> List[Dict]:
        """Process a single scraped file."""
        logger.info(f"Processing {file_path}")
        
        try:
            # Parse the file
            parsed = self.parse_scraped_file(file_path)
            
            # Chunk the content
            chunks = self.chunker.chunk_content(parsed['content'], parsed['title'])
            
            # Create output records
            records = []
            for i, chunk in enumerate(chunks):
                record = {
                    'id': f"{file_path.stem}_{i}",
                    'title': parsed['title'],
                    'url': parsed['url'],
                    'source': parsed['source'],
                    'text': chunk['text'],
                    'heading': chunk['heading'],
                    'level': chunk['level'],
                    'chunk_index': i,
                    'file_path': str(file_path.relative_to(self.input_dir)),
                    'timestamp': datetime.now().isoformat()
                }
                records.append(record)
            
            return records
            
        except Exception as e:
            logger.error(f"Error processing {file_path}: {e}")
            return []

    def process_directory(self, source_name: str = None) -> List[Dict]:
        """
        Process all .txt files in a directory or subdirectory.
        
        Args:
            source_name: Optional subdirectory name (e.g., 'openbis', 'wikijs')
        """
        all_records = []
        
        if source_name:
            search_dir = self.input_dir / source_name
            if not search_dir.exists():
                logger.warning(f"Directory {search_dir} does not exist")
                return all_records
        else:
            search_dir = self.input_dir
        
        # Find all .txt files
        txt_files = list(search_dir.glob('**/*.txt'))
        logger.info(f"Found {len(txt_files)} .txt files in {search_dir}")
        
        for file_path in txt_files:
            records = self.process_file(file_path)
            all_records.extend(records)
        
        return all_records

    def save_records(self, records: List[Dict], output_name: str = "processed_chunks"):
        """Save processed records to JSON and JSONL files."""
        if not records:
            logger.warning("No records to save")
            return
        
        # Save as JSON
        json_path = self.output_dir / f"{output_name}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
        
        # Save as JSONL
        jsonl_path = self.output_dir / f"{output_name}.jsonl"
        with open(jsonl_path, 'w', encoding='utf-8') as f:
            for record in records:
                f.write(json.dumps(record, ensure_ascii=False) + '\n')
        
        logger.info(f"Saved {len(records)} records to {json_path} and {jsonl_path}")

    def process_all_sources(self) -> Dict[str, List[Dict]]:
        """Process all sources and return organized results."""
        results = {}
        
        # Check for common source directories
        source_dirs = ['openbis', 'wikijs']
        
        for source_dir in source_dirs:
            source_path = self.input_dir / source_dir
            if source_path.exists():
                logger.info(f"Processing {source_dir} source...")
                records = self.process_directory(source_dir)
                results[source_dir] = records
                
                # Save source-specific results
                if records:
                    self.save_records(records, f"{source_dir}_chunks")
        
        # Combine all results
        all_records = []
        for source_records in results.values():
            all_records.extend(source_records)
        
        if all_records:
            self.save_records(all_records, "all_chunks")
        
        return results


def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Process scraped text files for RAG")
    parser.add_argument("--input-dir", default="ds_helper/data/raw", 
                       help="Input directory containing scraped files")
    parser.add_argument("--output-dir", default="ds_helper/data/processed", 
                       help="Output directory for processed chunks")
    parser.add_argument("--source", help="Specific source to process (openbis, wikijs)")
    parser.add_argument("--min-chunk-size", type=int, default=100,
                       help="Minimum chunk size in characters")
    parser.add_argument("--max-chunk-size", type=int, default=1000,
                       help="Maximum chunk size in characters")
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    processor = ScrapedTextProcessor(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        min_chunk_size=args.min_chunk_size,
        max_chunk_size=args.max_chunk_size
    )
    
    if args.source:
        records = processor.process_directory(args.source)
        processor.save_records(records, f"{args.source}_chunks")
    else:
        results = processor.process_all_sources()
        
        # Print summary
        total_chunks = sum(len(records) for records in results.values())
        print(f"\nProcessing Summary:")
        print(f"Total chunks created: {total_chunks}")
        for source, records in results.items():
            print(f"  {source}: {len(records)} chunks")


if __name__ == "__main__":
    main()
