Title: Add a hint in Parents and Children sections
URL: https://datastore.bam.de/en/How_to_guides/Add_hint_parents_and_children_sections
Source: datastore
---

/
How_to_guides
/
Add_hint_parents_and_children_sections
Add a hint in Parents and Children sections
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
06/10/2025
In the left main menu, under
Utilities
select
Settings
. The Select Group Settings drop-down menu will appear, select your
division number
to open your group settings. Click on the
Edit
tab, navigate to the Object type definitions Extension section, options to modify those sections within an Object type will be displayed. In the
Hints for
part of settings there is an option to set a particular Object type(s) as Parent(s) or Child(ren) and limit the number of them. By pressing the
+
tab on the right corner of the
Hints for
row, you can extend the number of the Parents and Children. In case, if minimal number of Parents and/or Children is specified, the required number of those is mandatory to enter and the form cannot be saved until this condition is satisfied. Annotation to these connections can be supported by using the Properties. To add Property press
+
left to the Parent(s)/Child(ren) field. Review the entries and
Save
.
Under Utilities
Select Settings
Select division number
Click on Edit tab
Navigate to Object type definitions Extension section
Select an Object type
In the Hints for part set Parent(s) and/or Child(ren) to the particular Object type
Specify minimal and maximal number of Parents and Children
Press + tab to add annotations
Review the entries and Save.