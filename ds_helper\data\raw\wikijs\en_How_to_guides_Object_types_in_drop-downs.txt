Title: Enable Object Types in drop-downs menus
URL: https://datastore.bam.de/en/How_to_guides/Object_types_in_drop-downs
Source: datastore
---

/
How_to_guides
/
Object_types_in_drop-downs
Enable Object Types in drop-downs menus
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
06/10/2025
In the left main menu, under
Utilities
select
Settings
. The Select Group Settings drop-down menu will appear, select your division number to open your group settings. Click on the
Edit
tab and scroll down to the
Object type definitions Extension
section, open the corresponding object type, and enable the
Show in drop-downs
option. You can edit several object types at the same time, review the changes and click on the Save.
Under Utilities
Select Settings
Select division number
Click on Edit tab
Scroll down to the section: Object type definitions Extension
Select an Object type
Enable Show in drop downs
Review the changes and Save.