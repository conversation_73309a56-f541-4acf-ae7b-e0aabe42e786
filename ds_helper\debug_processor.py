#!/usr/bin/env python3
"""
Debug the processor to understand why it's creating 0 chunks.
"""

import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ds_helper.processor.processor import MultiSourceRAGProcessor, ContentChunker
from ds_helper.utils.logging import setup_logging

def debug_processor():
    """Debug the processor with actual scraped files."""
    import logging
    setup_logging(logging.DEBUG)
    
    print("=== Debug Processor ===")
    
    # Check if we have scraped files
    input_dir = Path("data/raw")
    if not input_dir.exists():
        print("❌ No data/raw directory found")
        return False
    
    # Find all text files
    all_files = []
    for subdir in input_dir.iterdir():
        if subdir.is_dir():
            txt_files = list(subdir.glob("*.txt"))
            all_files.extend(txt_files)
    
    print(f"Found {len(all_files)} text files to process:")
    for f in all_files[:5]:  # Show first 5
        print(f"  - {f}")
    
    if not all_files:
        print("❌ No text files found")
        return False
    
    # Test the chunker directly with one file
    print(f"\n=== Testing Chunker Directly ===")
    test_file = all_files[0]
    print(f"Testing with: {test_file}")
    
    with open(test_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"File content length: {len(content)} characters")
    print(f"First 200 characters: {content[:200]}...")
    
    # Extract content after metadata
    if "---\n\n" in content:
        actual_content = content.split("---\n\n", 1)[-1]
        print(f"Content after metadata: {len(actual_content)} characters")
        print(f"First 200 characters after metadata: {actual_content[:200]}...")
    else:
        print("⚠️  No metadata separator found")
        actual_content = content
    
    # Test chunker
    chunker = ContentChunker(min_chunk_size=100, max_chunk_size=1000)
    chunks = chunker.chunk_content(actual_content)
    
    print(f"Chunker created {len(chunks)} chunks:")
    for i, chunk in enumerate(chunks):
        print(f"  Chunk {i+1}: {len(chunk)} characters")
        print(f"    Preview: {chunk[:100]}...")
    
    if len(chunks) == 0:
        print("⚠️  No chunks created. Let's debug the chunking process...")
        
        # Debug the chunking process
        paragraphs = [p for p in actual_content.split("\n\n") if p.strip()]
        print(f"Found {len(paragraphs)} paragraphs:")
        for i, p in enumerate(paragraphs[:5]):  # Show first 5
            print(f"  Paragraph {i+1}: {len(p)} chars - {p[:50]}...")
        
        # Test with lower minimum chunk size
        print(f"\nTesting with lower minimum chunk size...")
        chunker_low = ContentChunker(min_chunk_size=50, max_chunk_size=1000)
        chunks_low = chunker_low.chunk_content(actual_content)
        print(f"With min_chunk_size=50: {len(chunks_low)} chunks")
        
        # Test with even lower minimum
        chunker_very_low = ContentChunker(min_chunk_size=10, max_chunk_size=1000)
        chunks_very_low = chunker_very_low.chunk_content(actual_content)
        print(f"With min_chunk_size=10: {len(chunks_very_low)} chunks")
    
    # Test the full processor
    print(f"\n=== Testing Full Processor ===")
    processor = MultiSourceRAGProcessor(
        input_dir="data/raw",
        output_dir="debug_processor_output",
        min_chunk_size=50,  # Lower minimum for testing
        max_chunk_size=1000
    )
    
    # Process just one file
    try:
        chunks = processor.process_file(test_file)
        print(f"Processor created {len(chunks)} chunks from {test_file}")
        
        if chunks:
            sample = chunks[0]
            print(f"Sample chunk:")
            for key, value in sample.items():
                if key == 'embedding':
                    print(f"  {key}: [vector of length {len(value)}]")
                elif key == 'content':
                    print(f"  {key}: {value[:100]}...")
                else:
                    print(f"  {key}: {value}")
        
        return len(chunks) > 0
    except Exception as e:
        print(f"❌ Error processing file: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_processor()
    if success:
        print("\n✅ Processor debugging completed successfully!")
    else:
        print("\n❌ Processor has issues that need fixing.")
    
    sys.exit(0 if success else 1)
