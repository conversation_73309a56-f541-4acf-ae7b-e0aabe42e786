#!/usr/bin/env python3
"""
Minimal processor test.
"""

from pathlib import Path

def minimal_test():
    """Minimal test to check file discovery."""
    
    print("=== Minimal Processor Test ===")
    
    # Check if data/raw exists
    input_dir = Path("data/raw")
    print(f"Input directory exists: {input_dir.exists()}")
    
    if input_dir.exists():
        # List all subdirectories
        subdirs = [d for d in input_dir.iterdir() if d.is_dir()]
        print(f"Subdirectories: {[d.name for d in subdirs]}")
        
        # Find all .txt files recursively
        txt_files = list(input_dir.rglob("*.txt"))
        print(f"Found {len(txt_files)} .txt files:")
        for f in txt_files:
            print(f"  - {f}")
            
            # Check file size
            size = f.stat().st_size
            print(f"    Size: {size} bytes")
            
            # Check first few lines
            try:
                with open(f, 'r', encoding='utf-8') as file:
                    lines = file.readlines()[:5]
                    print(f"    First 5 lines:")
                    for i, line in enumerate(lines):
                        print(f"      {i+1}: {line.strip()}")
            except Exception as e:
                print(f"    Error reading file: {e}")
            print()
    
    # Check if processed directory exists
    output_dir = Path("data/processed")
    print(f"Output directory exists: {output_dir.exists()}")
    
    if output_dir.exists():
        files = list(output_dir.glob("*"))
        print(f"Files in output directory: {[f.name for f in files]}")

if __name__ == "__main__":
    minimal_test()
