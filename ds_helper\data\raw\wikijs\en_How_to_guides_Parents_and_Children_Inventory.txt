Title: Define Parents and Children of Inventory Objects
URL: https://datastore.bam.de/en/How_to_guides/Parents_and_Children_Inventory
Source: datastore
---

/
How_to_guides
/
Parents_and_Children_Inventory
Define Parents and Children of Inventory Objects
Last edited by
<PERSON><PERSON>, Angela
08/07/2025
To assign Parent and Children to Objects of a division’s private Inventory, select relevant
Object
, click on the
Edit
tab. In the
Parent
and
Children
section, click
Search Any
and select the
Type of the Object
(e.g., Instrument of the public BAM inventory) you want to add from the drop-down menu. Enter the
Code
or
Name
of the Object in the field, start typing to display available options for your group. Select and
Save
.
To define multiple Parents and Children at the same time, select the
Paste Any
option, add the
Code
or
Name
of respective Objects, review the entries and
Save
. You can copy the Code or Name of Objects from another ELN page (Log in to the BAM Data Store in another/private browser window). Paste the Codes(s) or name(s) in the text fields, review and
Save
.
Parent-Children connections are displayed in the
Hierarchy Graph
available under the
More
drop-down menu of each Object.
Select Object
Click on Edit tab
Navigate to the Parent and Children sections
Click Search Any
Select Object Type - Instrument
Enter Code or Name of the Objects to connect
Review the entries and Save.