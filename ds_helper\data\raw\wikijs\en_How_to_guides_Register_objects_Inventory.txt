Title: Register Objects in the Inventory
URL: https://datastore.bam.de/en/How_to_guides/Register_objects_Inventory
Source: datastore
---

/
How_to_guides
/
Register_objects_Inventory
Register Objects in the Inventory
Last edited by
<PERSON><PERSON>, Angela
08/07/2025
To register
Objects
in the Inventory, navigate to the relevant
Collection
, click on the
More
drop-down menu, select
New Object
and
Object Type
from drop-down menu.  Fill out the Object
Identification Info
(if hidden, open the
More
drop-down menu and select Show Identification Info). The
Code
is generated automatically for Objects and can
only
be changed during registration. Give the Object a meaningful
Name
, as this will be displayed to the users, review the entries and
Save
.
Note that you can register an object of type ‘Entry’ in the object form to quickly visualise information. A preview of text, images or tables is displayed in the
Document
icon of the Collection form. To see the icon, select relevant Collection from the left-hand menu.
Select Collection
Click on More drop-down menu
Select New Object
Select an Object Type
Fill out the Object form
Review the entries and Save.