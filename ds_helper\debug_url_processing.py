#!/usr/bin/env python3
"""
Debug URL processing to find the issue.
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

def debug_url_processing():
    """Debug the URL processing logic."""
    
    # Test with the actual URL
    base_url = "https://openbis.readthedocs.io/en/20.10.0-11/index.html"
    
    print(f"Base URL: {base_url}")
    print(f"Parsed base URL: {urlparse(base_url)}")
    
    # Get the page
    response = requests.get(base_url)
    soup = BeautifulSoup(response.text, "html.parser")
    
    print(f"\nFound links:")
    
    # Find all links
    links_found = []
    for link in soup.find_all("a", href=True):
        href = link["href"]
        
        # Skip empty links
        if not href:
            continue
            
        # Show the original href
        print(f"  Original href: '{href}'")
        
        # Convert relative URLs to absolute URLs
        if not href.startswith(("http://", "https://")):
            absolute_href = urljoin(base_url, href)
            print(f"    -> Absolute: '{absolute_href}'")
            links_found.append((href, absolute_href))
        else:
            print(f"    -> Already absolute: '{href}'")
            links_found.append((href, href))
        
        print()
    
    print(f"\nSummary of URL conversions:")
    for original, absolute in links_found[:20]:  # Show first 20
        if "user-documentation" in original or "software-developer" in original:
            print(f"  '{original}' -> '{absolute}'")
    
    # Test specific problematic URLs
    print(f"\nTesting specific URL conversions:")
    test_hrefs = [
        "user-documentation/general-users/index.html",
        "../user-documentation/general-users/index.html", 
        "./user-documentation/general-users/index.html",
        "/en/20.10.0-11/user-documentation/general-users/index.html"
    ]
    
    for test_href in test_hrefs:
        result = urljoin(base_url, test_href)
        print(f"  urljoin('{base_url}', '{test_href}') = '{result}'")

if __name__ == "__main__":
    debug_url_processing()
