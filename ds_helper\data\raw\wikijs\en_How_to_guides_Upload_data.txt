Title: Upload data
URL: https://datastore.bam.de/en/How_to_guides/Upload_data
Source: datastore
---

/
How_to_guides
/
Upload_data
Upload data
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
07/11/2025
The data uploaded in openBIS can be of any type and format, individual files, or data sets. Data uploaded is
immutable
, it cannot be changed, if necessary, different versions of the data must be uploaded. In the Data Store, it is suggested to upload data to the Experimental Steps - Objects. To upload data, navigate to relevant Experimental Step and click on the
Upload
tab. In the
Create Dataset
form, enter the Identification Info (if hidden, open the More drop-down menu and select Show Identification Info).  Select
Dataset Type
if not defined, e.g. Attachment. Fill out the relevant information and click on select
files to upload
, drag and drop or browse files or upload a zip file (select Uncompress-checkbox before import), review the files and
Save
.  Datasets are displayed in the left-lower corner of the main menu.
Uploaded Datasets are displayed in the left-hand menu. To view the contents of Datasets, click on the Dataset and download it. Please note that there is no preview function for Datasets in openBIS.
Alternatively, you can preview text, images, or tables by registering an Object of type
Entry
or registering an Object using
Templates
.
Select Experimental Step
Click Upload tab
Select Dataset Type (*), e.g. Attachment
Select files to upload
(drag and drop, browse or upload zip file
and select Uncompress-checkbox  before import)
Review the files and Save.
Select Experimental Step
Click Upload button
Select Entry or Dataset Type (*), e.g. Attachment
Select files to upload
(drag and drop, browse or upload zip file
and select Uncompress-checkbox  before import)
Review the files and Save.