Title: Enable Barcode / QR-Code Functionality
URL: https://datastore.bam.de/en/How_to_guides/Enable_barcodes
Source: datastore
---

/
How_to_guides
/
Enable_barcodes
Enable Barcode / QR-Code Functionality
Page Contents
🧩 Enable Barcode / QR-Code Functionality
✅ Prerequisites
🪪 Step 1: Understand Default Barcode Behavior
⚙️ Step 2: Enable Barcode Display for Your Group
🔄 Step 3: Reload the Interface
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
06/24/2025
¶
🧩 Enable Barcode / QR-Code Functionality
To make barcodes and QR codes visible and usable for your group in openBIS.
¶
✅ Prerequisites
You must have Group Admin role (e.g., be Data Store Steward (DSSt)
Your group must be registered in the Data Store.
At least one object (e.g., sample, instrument, chemical) must be registered.
¶
🪪 Step 1: Understand Default Barcode Behavior
When an object is registered in openBIS, a
default barcode
is automatically generated.
This barcode is visible in the
Identification Info
section.
If it's not visible:
Click the
More
drop-down menu.
Select
Show Identification Info
.
¶
⚙️ Step 2: Enable Barcode Display for Your Group
In the
left main menu
, go to
Utilities
.
Click on
Settings
.
In the
Select Group Settings
drop-down, choose your
division number
.
Click the
Edit
tab.
Scroll down to the
Main menu
section.
Check the box labeled
Show Barcodes
.
Review your changes and click
Save
.
¶
🔄 Step 3: Reload the Interface
Refresh the openBIS webpage.