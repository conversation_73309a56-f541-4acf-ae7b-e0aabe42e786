#!/usr/bin/env python3
"""
Test script for the scraped text processor.

This script tests the new ScrapedTextProcessor on a small sample of data
to verify that the chunking works correctly.
"""

import sys
import logging
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ds_helper.processor.scraped_text_processor import ScrapedTextProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_processor():
    """Test the processor with a small sample."""
    
    # Set up paths
    input_dir = Path("ds_helper/data/raw")
    output_dir = Path("ds_helper/data/test_processed")
    
    print(f"Testing ScrapedTextProcessor...")
    print(f"Input directory: {input_dir}")
    print(f"Output directory: {output_dir}")
    
    # Create processor
    processor = ScrapedTextProcessor(
        input_dir=str(input_dir),
        output_dir=str(output_dir),
        min_chunk_size=100,
        max_chunk_size=800  # Smaller for testing
    )
    
    # Test with a few files from each source
    test_files = []
    
    # Find some test files
    openbis_dir = input_dir / "openbis"
    wikijs_dir = input_dir / "wikijs"
    
    if openbis_dir.exists():
        openbis_files = list(openbis_dir.glob("*.txt"))[:3]  # First 3 files
        test_files.extend(openbis_files)
        print(f"Found {len(openbis_files)} openBIS test files")
    
    if wikijs_dir.exists():
        wikijs_files = list(wikijs_dir.glob("*.txt"))[:3]  # First 3 files
        test_files.extend(wikijs_files)
        print(f"Found {len(wikijs_files)} Wiki.js test files")
    
    if not test_files:
        print("No test files found!")
        return
    
    # Process individual files for detailed testing
    all_records = []
    for file_path in test_files:
        print(f"\n--- Processing {file_path.name} ---")
        
        # Parse the file first
        parsed = processor.parse_scraped_file(file_path)
        print(f"Title: {parsed['title']}")
        print(f"Source: {parsed['source']}")
        print(f"Content length: {len(parsed['content'])} characters")
        
        # Process the file
        records = processor.process_file(file_path)
        print(f"Generated {len(records)} chunks")
        
        # Show first chunk as example
        if records:
            first_chunk = records[0]
            print(f"First chunk preview:")
            print(f"  Heading: {first_chunk['heading']}")
            print(f"  Level: {first_chunk['level']}")
            print(f"  Text length: {len(first_chunk['text'])} characters")
            print(f"  Text preview: {first_chunk['text'][:200]}...")
        
        all_records.extend(records)
    
    # Save test results
    if all_records:
        processor.save_records(all_records, "test_sample")
        print(f"\n--- Test Summary ---")
        print(f"Total files processed: {len(test_files)}")
        print(f"Total chunks created: {len(all_records)}")
        print(f"Results saved to: {output_dir}")
        
        # Show chunk size distribution
        chunk_sizes = [len(record['text']) for record in all_records]
        print(f"Chunk size stats:")
        print(f"  Min: {min(chunk_sizes)} characters")
        print(f"  Max: {max(chunk_sizes)} characters")
        print(f"  Average: {sum(chunk_sizes) // len(chunk_sizes)} characters")
        
        # Show source distribution
        sources = {}
        for record in all_records:
            source = record['source']
            sources[source] = sources.get(source, 0) + 1
        
        print(f"Chunks by source:")
        for source, count in sources.items():
            print(f"  {source}: {count} chunks")
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_processor()
