Title: Allocate storage position to a single Object
URL: https://datastore.bam.de/en/How_to_guides/Register_storage_position
Source: datastore
---

/
How_to_guides
/
Register_storage_position
Allocate storage position to a single Object
Last edited by
<PERSON><PERSON>, Angela
08/11/2025
The digital representation of laboratory storage must be configured by the Data Store Stewards (DSSt(s)) in your division. To add storage information to an
Object
during registration, navigate to the
Storage
section in the
Object
form.  If the Object is already registered, navigate to the relevant Object, click
Edit
and scroll down to the
Storage
section.  Click on the
+ New Storage Position
Tab. The
Physical Storage
form opens. Select
Storage
from the drop-down menu, specify the
position
of the Object (e.g., <PERSON>ck, Box name to display Box position) mark the position of the Object within the Box, click on the
Accept
tab and then on the
Save
tab.
Select Object
Click on Edit tab
Navigate to Storage sections
Click + New Storage Position
Specify Object Position (e.g., Rack, Box)
Mark the Object(s) position(s) within the Box
Click on Accept tab
Save Object form.