#!/usr/bin/env python3
"""
Cleanup script to remove all generated data and start fresh.
"""

import shutil
from pathlib import Path

def cleanup():
    """Remove all generated data."""
    paths_to_remove = [
        "data",
        "ds_helper_vectordb", 
        "test_output",
        "ds_helper_conversation_memory.db"
    ]
    
    for path_str in paths_to_remove:
        path = Path(path_str)
        if path.exists():
            if path.is_dir():
                shutil.rmtree(path)
                print(f"Removed directory: {path}")
            else:
                path.unlink()
                print(f"Removed file: {path}")
        else:
            print(f"Path doesn't exist: {path}")
    
    print("Cleanup complete!")

if __name__ == "__main__":
    cleanup()
