Title: Enable Barcodes and QR codes
URL: https://openbis.readthedocs.io/en/20.10.0-11/user-documentation/general-admin-users/admins-documentation/enable-barcodes.html
Source: openbis
---

Enable Barcodes and QR codes

In order to be able to add custom barcodes and QR codes to
Objects
, an
Instance
Admin
needs to add the $BARCODE property to the object type for which
barcodes/QR codes are needed.
The barcode functionality is disabled by default in the ELN UI. This can
be enabled by a
lab manager
or a
group admin
with admin right to
edit the
Settings
, as shown below.
After enabling the option, please refresh your browser. The
Barcodes/QR Codes Generator
will be shown in the main menu under
Utilities
and a
barcode icon will be added above the menu.
Information on how to use the Barcode functionality in openBIS can be
found
here:
Barcodes
Updated on April 26, 2023